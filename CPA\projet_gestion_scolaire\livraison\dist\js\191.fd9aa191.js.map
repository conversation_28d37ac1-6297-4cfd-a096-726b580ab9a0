{"version": 3, "file": "js/191.fd9aa191.js", "mappings": "qOACOA,MAAM,uB,GAGJA,MAAM,Q,GAEJA,MAAM,mB,GACJA,MAAM,c,EAPnB,U,GAiBaA,MAAM,c,EAjBnB,a,EAAA,U,GA2BaA,MAAM,c,EA3BnB,a,EAAA,U,GAqCaA,MAAM,c,GAQNA,MAAM,c,GAEJA,MAAM,iB,GAINA,MAAM,iB,GAINA,MAAM,iB,GAMRA,MAAM,gB,EA7DnB,a,EAAA,a,GAAAC,IAAA,EAwEmCD,MAAM,a,GAC9BA,MAAM,uB,GAEJA,MAAM,kB,GAORA,MAAM,kB,GAEFA,MAAM,iB,GACHA,MAAM,iB,GACNA,MAAM,oB,GAtFxBC,IAAA,EAuG4BD,MAAM,4B,GACvBA,MAAM,oB,GACJA,MAAM,kB,GAMNA,MAAM,mB,GAEFA,MAAM,mB,GACJA,MAAM,e,GAKNA,MAAM,gB,GAQRA,MAAM,iB,GACFA,MAAM,gB,GAhI3BC,IAAA,EAgLmDD,MAAM,kB,GAhLzDC,IAAA,EAuLsDD,MAAM,yB,GAM3CA,MAAM,mB,GACJA,MAAM,mB,GA9LzBC,IAAA,EAmMqDD,MAAM,kB,GAnM3DC,IAAA,EAuMqDD,MAAM,kB,GAQ9CA,MAAM,mB,0CA9MjBE,EAAAA,EAAAA,IAsNM,MAtNNC,EAsNM,gBArNJC,EAAAA,EAAAA,IAAiC,UAA7B,4BAAwB,KAE5BA,EAAAA,EAAAA,IAkEM,MAlENC,EAkEM,gBAjEJD,EAAAA,EAAAA,IAAiC,UAA7B,4BAAwB,KAC5BA,EAAAA,EAAAA,IA+DM,MA/DNE,EA+DM,EA9DJF,EAAAA,EAAAA,IAQM,MARNG,EAQM,gBAPJH,EAAAA,EAAAA,IAAkC,SAA3BI,IAAI,UAAS,UAAM,cAC1BJ,EAAAA,EAAAA,IAKS,UALDK,GAAG,SATrB,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GASuCC,EAAAC,iBAAgBF,GAAEG,SAAA,I,gBAC7CV,EAAAA,EAAAA,IAAyD,UAAjDW,MAAM,GAAGC,SAAA,IAAS,0BAAsB,mBAChDd,EAAAA,EAAAA,IAESe,EAAAA,GAAA,MAbrBC,EAAAA,EAAAA,IAWqCN,EAAAO,SAAVC,K,WAAflB,EAAAA,EAAAA,IAES,UAF0BD,IAAKmB,EAAOX,GAAKM,MAAOK,EAAOX,K,QAC7DW,EAAOC,MAAO,MAAEC,EAAAA,EAAAA,IAAGC,EAAAC,WAAWJ,EAAOK,YAAa,OAAGH,EAAAA,EAAAA,IAAGC,EAAAC,WAAWJ,EAAOM,UAAW,KAC1F,EAbZC,M,mBASuCf,EAAAC,uBAQ/BT,EAAAA,EAAAA,IAQM,MARNwB,EAQM,gBAPJxB,EAAAA,EAAAA,IAAkC,SAA3BI,IAAI,UAAS,UAAM,cAC1BJ,EAAAA,EAAAA,IAKS,UALDK,GAAG,SAnBrB,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GAmBuCC,EAAAiB,iBAAgBlB,GAAGK,UAAWJ,EAAAC,iBAAkBC,SAAA,I,gBAC3EV,EAAAA,EAAAA,IAA0D,UAAlDW,MAAM,GAAGC,SAAA,IAAS,2BAAuB,mBACjDd,EAAAA,EAAAA,IAESe,EAAAA,GAAA,MAvBrBC,EAAAA,EAAAA,IAqBqCK,EAAAO,kBAAVC,K,WAAf7B,EAAAA,EAAAA,IAES,UAFmCD,IAAK8B,EAAOtB,GAAKM,MAAOgB,EAAOtB,K,QACtEsB,EAAOC,KAAM,MAAEV,EAAAA,EAAAA,IAAGS,EAAOE,UAAW,YACzC,EAvBZC,M,SAAAC,GAAA,OAmBuCvB,EAAAiB,uBAQ/BzB,EAAAA,EAAAA,IAQM,MARNgC,EAQM,gBAPJhC,EAAAA,EAAAA,IAA4C,SAArCI,IAAI,SAAQ,qBAAiB,cACpCJ,EAAAA,EAAAA,IAKS,UALDK,GAAG,QA7BrB,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GA6BsCC,EAAAyB,gBAAe1B,GAAGK,UAAWJ,EAAAiB,kB,gBACvDzB,EAAAA,EAAAA,IAAyC,UAAjCW,MAAM,IAAG,mBAAe,mBAChCb,EAAAA,EAAAA,IAESe,EAAAA,GAAA,MAjCrBC,EAAAA,EAAAA,IA+BoCK,EAAAe,iBAATC,K,WAAfrC,EAAAA,EAAAA,IAES,UAFiCD,IAAKsC,EAAM9B,GAAKM,MAAOwB,EAAM9B,K,QAClE8B,EAAMP,KAAM,KAACV,EAAAA,EAAAA,IAAGiB,EAAMC,QAAS,MAAElB,EAAAA,EAAAA,IAAGiB,EAAME,WAAY,KAC3D,EAjCZC,M,SAAAC,GAAA,OA6BsC/B,EAAAyB,sBAQ9BjC,EAAAA,EAAAA,IAMM,MANNwC,EAMM,gBALJxC,EAAAA,EAAAA,IAAkC,SAA3BI,IAAI,UAAS,UAAM,cAC1BJ,EAAAA,EAAAA,IAGS,UAHDK,GAAG,SAvCrB,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GAuCuCC,EAAAiC,OAAMlC,GAAEG,SAAA,I,gBACnCV,EAAAA,EAAAA,IAAuC,UAA/BW,MAAM,MAAK,eAAW,IAC9BX,EAAAA,EAAAA,IAAuC,UAA/BW,MAAM,MAAK,eAAW,iBAFHH,EAAAiC,aAM/BzC,EAAAA,EAAAA,IAcM,MAdN0C,EAcM,gBAbJ1C,EAAAA,EAAAA,IAAsB,aAAf,WAAO,KACdA,EAAAA,EAAAA,IAGM,MAHN2C,EAGM,WAFJ3C,EAAAA,EAAAA,IAAgF,SAAzEiB,KAAK,WAAWZ,GAAG,mBAhDtC,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GAgDkEC,EAAAoC,QAAQC,iBAAgBtC,I,iBAAxBC,EAAAoC,QAAQC,oBAAgB,eAC9E7C,EAAAA,EAAAA,IAAoE,SAA7DI,IAAI,oBAAmB,kCAA8B,OAE9DJ,EAAAA,EAAAA,IAGM,MAHN8C,EAGM,WAFJ9C,EAAAA,EAAAA,IAAgF,SAAzEiB,KAAK,WAAWZ,GAAG,mBApDtC,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GAoDkEC,EAAAoC,QAAQG,iBAAgBxC,I,iBAAxBC,EAAAoC,QAAQG,oBAAgB,eAC9E/C,EAAAA,EAAAA,IAAsE,SAA/DI,IAAI,oBAAmB,oCAAgC,OAEhEJ,EAAAA,EAAAA,IAGM,MAHNgD,EAGM,WAFJhD,EAAAA,EAAAA,IAAsF,SAA/EiB,KAAK,WAAWZ,GAAG,sBAxDtC,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GAwDqEC,EAAAoC,QAAQK,oBAAmB1C,I,iBAA3BC,EAAAoC,QAAQK,uBAAmB,eACpFjD,EAAAA,EAAAA,IAAsE,SAA/DI,IAAI,uBAAsB,iCAA6B,SAIlEJ,EAAAA,EAAAA,IAOM,MAPNkD,EAOM,EANJlD,EAAAA,EAAAA,IAES,UAFAmD,QAAK7C,EAAA,KAAAA,EAAA,OAAA8C,IAAEjC,EAAAkC,iBAAAlC,EAAAkC,mBAAAD,IAAiBxD,MAAM,oBAAqBgB,UAAWO,EAAAmC,qB,gBACrEtD,EAAAA,EAAAA,IAA0B,KAAvBJ,MAAM,cAAY,UA/DjC2D,EAAAA,EAAAA,IA+DsC,cAC5B,EAhEVC,IAiEUxD,EAAAA,EAAAA,IAES,UAFAmD,QAAK7C,EAAA,KAAAA,EAAA,OAAA8C,IAAEjC,EAAAsC,mBAAAtC,EAAAsC,qBAAAL,IAAmBxD,MAAM,kBAAmBgB,UAAWO,EAAAmC,qB,gBACrEtD,EAAAA,EAAAA,IAA+B,KAA5BJ,MAAM,mBAAiB,UAlEtC2D,EAAAA,EAAAA,IAkE2C,mBACjC,EAnEVG,SAwEelD,EAAAmD,qBAAkB,WAA7B7D,EAAAA,EAAAA,IA6BM,MA7BN8D,EA6BM,EA5BJ5D,EAAAA,EAAAA,IAOM,MAPN6D,EAOM,gBANJ7D,EAAAA,EAAAA,IAA0B,UAAtB,qBAAiB,KACrBA,EAAAA,EAAAA,IAIM,MAJN8D,EAIM,EAHJ9D,EAAAA,EAAAA,IAES,UAFAmD,QAAK7C,EAAA,KAAAA,EAAA,OAAA8C,IAAEjC,EAAA4C,sBAAA5C,EAAA4C,wBAAAX,IAAsBxD,MAAM,mB,gBAC1CI,EAAAA,EAAAA,IAA+B,KAA5BJ,MAAM,mBAAiB,UA7EtC2D,EAAAA,EAAAA,IA6E2C,8BAKrCvD,EAAAA,EAAAA,IAkBM,MAlBNgE,EAkBM,gBAjBJlE,EAAAA,EAAAA,IAgBMe,EAAAA,GAAA,MAnGdC,EAAAA,EAAAA,IAmFyCN,EAAAyD,oBAnFzC,CAmFqBC,EAAUC,M,WAAvBrE,EAAAA,EAAAA,IAgBM,OAhBgDD,IAAKsE,EAAOvE,MAAM,iB,EACtEI,EAAAA,EAAAA,IAGM,MAHNoE,EAGM,EAFJpE,EAAAA,EAAAA,IAA2D,OAA3DqE,GAA2DnD,EAAAA,EAAAA,IAA5BgD,EAASI,WAAS,IACjDtE,EAAAA,EAAAA,IAAmF,OAAnFuE,GAAmFrD,EAAAA,EAAAA,IAAjDgD,EAASvC,QAAS,OAAGT,EAAAA,EAAAA,IAAGgD,EAASlD,QAAM,oBAtFrFwD,EAAAA,EAAAA,IAAA,+Y,YAAAC,EAAAA,EAAAA,IAAA,OAuGejE,EAAAkE,cAAW,WAAtB5E,EAAAA,EAAAA,IA+GM,MA/GN6E,EA+GM,EA9GJ3E,EAAAA,EAAAA,IA6GM,MA7GN4E,EA6GM,EA5GJ5E,EAAAA,EAAAA,IAKM,MALN6E,EAKM,gBAJJ7E,EAAAA,EAAAA,IAA2B,UAAvB,sBAAkB,KACtBA,EAAAA,EAAAA,IAES,UAFAmD,QAAK7C,EAAA,MAAAA,EAAA,QAAA8C,IAAEjC,EAAA2D,cAAA3D,EAAA2D,gBAAA1B,IAAcxD,MAAM,a,gBAClCI,EAAAA,EAAAA,IAA4B,KAAzBJ,MAAM,gBAAc,eAG3BI,EAAAA,EAAAA,IA+FM,MA/FN+E,EA+FM,EA9FJ/E,EAAAA,EAAAA,IA6FM,OA7FDJ,OAhHfoF,EAAAA,EAAAA,IAAA,CAgHqB,oBAA4BxE,EAAAiC,U,EACrCzC,EAAAA,EAAAA,IAYM,MAZNiF,EAYM,EAXJjF,EAAAA,EAAAA,IAIM,MAJNkF,EAIM,gBAHJlF,EAAAA,EAAAA,IAA+C,UAA3C,0CAAsC,mBAC1CA,EAAAA,EAAAA,IAAgC,SAA7B,6BAAyB,KAC5BA,EAAAA,EAAAA,IAAoD,UAAAkB,EAAAA,EAAAA,IAA9CC,EAAAgE,cAAgBhE,EAAAgE,cAAclE,KAAO,IAAH,MAE1CjB,EAAAA,EAAAA,IAKM,MALNoF,EAKM,EAJJpF,EAAAA,EAAAA,IAAwH,yBAArHA,EAAAA,EAAAA,IAA+B,cAAvB,kBAAc,KAxHzCuD,EAAAA,EAAAA,IAwHkD,KAACrC,EAAAA,EAAAA,IAAGC,EAAAkE,aAAe,GAAGlE,EAAAkE,aAAazD,OAAOT,EAAAkE,aAAajD,SAAW,eAAL,MAC/FpC,EAAAA,EAAAA,IAAuF,yBAApFA,EAAAA,EAAAA,IAA2B,cAAnB,cAAU,KAzHrCuD,EAAAA,EAAAA,IAyH8C,KAACrC,EAAAA,EAAAA,IAAGC,EAAAkE,aAAelE,EAAAkE,aAAahD,UAAY,QAAH,MACvErC,EAAAA,EAAAA,IAAkF,yBAA/EA,EAAAA,EAAAA,IAAwB,cAAhB,WAAO,KA1HlCuD,EAAAA,EAAAA,IA0H2C,KAACrC,EAAAA,EAAAA,IAAGC,EAAAmE,cAAgBnE,EAAAmE,cAAc1D,IAAM,UAAH,MAChE5B,EAAAA,EAAAA,IAAqF,yBAAlFA,EAAAA,EAAAA,IAA0B,cAAlB,aAAS,KA3HpCuD,EAAAA,EAAAA,IA2H6C,KAACrC,EAAAA,EAAAA,IAAGC,EAAAmE,cAAgBnE,EAAAmE,cAAczD,SAAW,MAAH,UAI3E7B,EAAAA,EAAAA,IA4DM,MA5DNuF,EA4DM,EA3DJvF,EAAAA,EAAAA,IAuBQ,QAvBRwF,EAuBQ,gBAtBNxF,EAAAA,EAAAA,IAUQ,eATNA,EAAAA,EAAAA,IAQK,YAPHA,EAAAA,EAAAA,IAAwB,UAApB,oBACJA,EAAAA,EAAAA,IAAkB,UAAd,cACJA,EAAAA,EAAAA,IAAkB,UAAd,cACJA,EAAAA,EAAAA,IAAqB,UAAjB,iBACJA,EAAAA,EAAAA,IAAsB,UAAlB,kBACJA,EAAAA,EAAAA,IAAsB,UAAlB,kBACJA,EAAAA,EAAAA,IAAkB,UAAd,iBAAS,KAGjBA,EAAAA,EAAAA,IAUQ,6BATNF,EAAAA,EAAAA,IAQKe,EAAAA,GAAA,MArJvBC,EAAAA,EAAAA,IA6IiDN,EAAAiF,UA7IjD,CA6I8BC,EAASvB,M,WAArBrE,EAAAA,EAAAA,IAQK,MARqCD,IAAKsE,GAAK,EAClDnE,EAAAA,EAAAA,IAAsD,WAAAkB,EAAAA,EAAAA,IAA/CwE,EAAQ9D,KAAM,MAAEV,EAAAA,EAAAA,IAAGwE,EAAQC,aAAc,IAAC,IACjD3F,EAAAA,EAAAA,IAA8B,WAAAkB,EAAAA,EAAAA,IAAvBC,EAAAyE,iBAAa,IACpB5F,EAAAA,EAAAA,IAA8B,WAAAkB,EAAAA,EAAAA,IAAvBC,EAAAyE,iBAAa,IACpB5F,EAAAA,EAAAA,IAA8B,WAAAkB,EAAAA,EAAAA,IAAvBC,EAAAyE,iBAAa,IACpB5F,EAAAA,EAAAA,IAAiC,WAAAkB,EAAAA,EAAAA,IAA1BC,EAAA0E,oBAAgB,IACvB7F,EAAAA,EAAAA,IAAoE,WAAAkB,EAAAA,EAAAA,KAA5DC,EAAA0E,mBAAqBH,EAAQC,aAAaG,QAAQ,IAAD,IACzD9F,EAAAA,EAAAA,IAAgC,WAAAkB,EAAAA,EAAAA,IAAzBC,EAAA4E,mBAAe,Q,yBApJ1CvB,EAAAA,EAAAA,IAAA,6sBAgLyBhE,EAAAoC,QAAQG,mBAAgB,WAAnCjD,EAAAA,EAAAA,IAKM,MALNkG,EAKM1F,EAAA,MAAAA,EAAA,MAJJN,EAAAA,EAAAA,IAAoD,KAAjDJ,MAAM,eAAc,6BAAyB,IAChDI,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,qBAAoB,gCAE/B,QApLhB6E,EAAAA,EAAAA,IAAA,OAuLyBjE,EAAAoC,QAAQK,sBAAmB,WAAtCnD,EAAAA,EAAAA,IAGM,MAHNmG,EAGM3F,EAAA,MAAAA,EAAA,MAFJN,EAAAA,EAAAA,IAAuD,KAApDJ,MAAM,sBAAqB,yBAAqB,IACnDI,EAAAA,EAAAA,IAAsH,KAAnHJ,MAAM,wBAAuB,sFAAkF,QAzLlI6E,EAAAA,EAAAA,IAAA,UA6LYzE,EAAAA,EAAAA,IAeM,MAfNkG,EAeM,EAdJlG,EAAAA,EAAAA,IAaM,MAbNmG,EAaM,gBAZJnG,EAAAA,EAAAA,IAGM,OAHDJ,MAAM,kBAAgB,EACzBI,EAAAA,EAAAA,IAA2B,SAAxB,yBACHA,EAAAA,EAAAA,IAAkC,OAA7BJ,MAAM,qBAAgB,IAElBY,EAAAoC,QAAQC,mBAAgB,WAAnC/C,EAAAA,EAAAA,IAGM,MAHNsG,EAGM9F,EAAA,MAAAA,EAAA,MAFJN,EAAAA,EAAAA,IAAiB,SAAd,cAAU,IACbA,EAAAA,EAAAA,IAAkC,OAA7BJ,MAAM,kBAAgB,cArM7C6E,EAAAA,EAAAA,IAAA,OAuM2BjE,EAAAoC,QAAQC,mBAAgB,WAAnC/C,EAAAA,EAAAA,IAGM,MAHNuG,EAGM/F,EAAA,MAAAA,EAAA,MAFJN,EAAAA,EAAAA,IAAgC,SAA7B,6BAAyB,IAC5BA,EAAAA,EAAAA,IAAmC,OAA9BJ,MAAM,mBAAiB,cAzM9C6E,EAAAA,EAAAA,IAAA,kBA+MQzE,EAAAA,EAAAA,IAKM,MALNsG,EAKM,EAJJtG,EAAAA,EAAAA,IAAuE,UAA9DmD,QAAK7C,EAAA,MAAAA,EAAA,QAAA8C,IAAEjC,EAAA2D,cAAA3D,EAAA2D,gBAAA1B,IAAcxD,MAAM,qBAAoB,WACxDI,EAAAA,EAAAA,IAES,UAFAmD,QAAK7C,EAAA,MAAAA,EAAA,QAAA8C,IAAEjC,EAAAsC,mBAAAtC,EAAAsC,qBAAAL,IAAmBxD,MAAM,mB,gBACvCI,EAAAA,EAAAA,IAA+B,KAA5BJ,MAAM,mBAAiB,UAlNtC2D,EAAAA,EAAAA,IAkN2C,4BAlN3CkB,EAAAA,EAAAA,IAAA,Q,4CA2NA,GACE8B,KAAM,sBACNC,IAAAA,GACE,MAAO,CACL/F,iBAAkB,GAClBgB,iBAAkB,GAClBQ,gBAAiB,GACjBQ,OAAQ,KACRG,QAAS,CACPC,kBAAkB,EAClBE,kBAAkB,EAClBE,qBAAqB,GAGvBlC,QAAS,CACP,CACEV,GAAI,EACJY,KAAM,cACNI,UAAW,aACXC,QAAS,aACTmF,WAAY,CAAC,EAAG,GAChBC,YAAY,GAEd,CACErG,GAAI,EACJY,KAAM,cACNI,UAAW,aACXC,QAAS,aACTmF,WAAY,CAAC,EAAG,GAChBC,YAAY,IAIhBC,QAAS,CACP,CAAEtG,GAAI,EAAGuB,IAAK,SAAUC,SAAU,IAClC,CAAExB,GAAI,EAAGuB,IAAK,SAAUC,SAAU,IAClC,CAAExB,GAAI,EAAGuB,IAAK,SAAUC,SAAU,IAClC,CAAExB,GAAI,EAAGuB,IAAK,SAAUC,SAAU,KAGpC+E,OAAQ,CACN,CAAEvG,GAAI,EAAGuB,IAAK,SAAUQ,OAAQ,OAAQC,UAAW,OAAQwE,SAAU,GACrE,CAAExG,GAAI,EAAGuB,IAAK,SAAUQ,OAAQ,SAAUC,UAAW,OAAQwE,SAAU,GACvE,CAAExG,GAAI,EAAGuB,IAAK,SAAUQ,OAAQ,SAAUC,UAAW,OAAQwE,SAAU,GACvE,CAAExG,GAAI,EAAGuB,IAAK,WAAYQ,OAAQ,QAASC,UAAW,OAAQwE,SAAU,GACxE,CAAExG,GAAI,EAAGuB,IAAK,SAAUQ,OAAQ,QAASC,UAAW,OAAQwE,SAAU,IAGxEpB,SAAU,CACR,CAAEpF,GAAI,EAAGuB,IAAK,gBAAiBkF,YAAa,OAAQnB,YAAa,GACjE,CAAEtF,GAAI,EAAGuB,IAAK,WAAYkF,YAAa,MAAOnB,YAAa,GAC3D,CAAEtF,GAAI,EAAGuB,IAAK,WAAYkF,YAAa,KAAMnB,YAAa,GAC1D,CAAEtF,GAAI,EAAGuB,IAAK,sBAAuBkF,YAAa,OAAQnB,YAAa,GACvE,CAAEtF,GAAI,EAAGuB,IAAK,UAAWkF,YAAa,MAAOnB,YAAa,GAC1D,CAAEtF,GAAI,EAAGuB,IAAK,MAAOkF,YAAa,MAAOnB,YAAa,GACtD,CAAEtF,GAAI,EAAGuB,IAAK,qBAAsBkF,YAAa,MAAOnB,YAAa,GACrE,CAAEtF,GAAI,EAAGuB,IAAK,kBAAmBkF,YAAa,MAAOnB,YAAa,IAGpEjB,aAAa,EACbf,oBAAoB,EACpBM,mBAAoB,GAExB,EAEA8C,SAAU,CACRrF,gBAAAA,GACE,IAAKsF,KAAKvG,iBAAkB,MAAO,GAEnC,MAAMO,EAASgG,KAAKjG,QAAQkG,MAAKC,GAAKA,EAAE7G,KAAO2G,KAAKvG,mBACpD,OAAKO,EAEEgG,KAAKL,QAAQQ,QAAOC,GAAKpG,EAAOyF,WAAWY,SAASD,EAAE/G,MAFzC,EAGtB,EAEA6B,eAAAA,GACE,OAAK8E,KAAKvF,iBACHuF,KAAKJ,OAAOO,QAAOD,GAAKA,EAAEL,WAAaG,KAAKvF,mBADhB,EAErC,EAEA6B,mBAAAA,GACE,OAAO0D,KAAKvG,kBAAoBuG,KAAKvF,gBACvC,EAEA0D,aAAAA,GACE,OAAO6B,KAAKjG,QAAQkG,MAAKC,GAAKA,EAAE7G,KAAO2G,KAAKvG,kBAC9C,EAEA6E,aAAAA,GACE,OAAO0B,KAAKL,QAAQM,MAAKG,GAAKA,EAAE/G,KAAO2G,KAAKvF,kBAC9C,EAEA4D,YAAAA,GACE,OAAK2B,KAAK/E,gBACH+E,KAAKJ,OAAOK,MAAKC,GAAKA,EAAE7G,KAAO2G,KAAK/E,kBADT,IAEpC,GAGFqF,QAAS,CACPjE,eAAAA,GACE2D,KAAKtC,aAAc,CACrB,EAEAI,YAAAA,GACEkC,KAAKtC,aAAc,CACrB,EAEAjB,iBAAAA,GAME,GAFAuD,KAAK/C,mBAAqB,GAEtB+C,KAAK/E,gBAAiB,CAExB,MAAME,EAAQ6E,KAAKJ,OAAOK,MAAKC,GAAKA,EAAE7G,KAAO2G,KAAK/E,kBAC5CN,EAASqF,KAAKL,QAAQM,MAAKG,GAAKA,EAAE/G,KAAO2G,KAAKvF,mBAC9CT,EAASgG,KAAKjG,QAAQkG,MAAKC,GAAKA,EAAE7G,KAAO2G,KAAKvG,mBAEhD0B,GAASR,GAAUX,GACrBgG,KAAK/C,mBAAmBsD,KAAK,CAC3BlH,GAAImH,KAAKC,MACTC,QAASvF,EAAM9B,GACfiE,UAAW,GAAGnC,EAAMP,OAAOO,EAAMC,SACjCT,OAAQA,EAAOC,IACfZ,OAAQA,EAAOC,KACfwB,OAAQuE,KAAKvE,OACbG,QAAS,IAAKoE,KAAKpE,UAGzB,KAAO,CAEL,MAAMjB,EAASqF,KAAKL,QAAQM,MAAKG,GAAKA,EAAE/G,KAAO2G,KAAKvF,mBAC9CT,EAASgG,KAAKjG,QAAQkG,MAAKC,GAAKA,EAAE7G,KAAO2G,KAAKvG,mBAC9CkH,EAAcX,KAAKJ,OAAOO,QAAOD,GAAKA,EAAEL,WAAaG,KAAKvF,mBAE5DE,GAAUX,GACZ2G,EAAYC,SAAQzF,IAClB6E,KAAK/C,mBAAmBsD,KAAK,CAC3BlH,GAAImH,KAAKC,MAAQtF,EAAM9B,GACvBqH,QAASvF,EAAM9B,GACfiE,UAAW,GAAGnC,EAAMP,OAAOO,EAAMC,SACjCT,OAAQA,EAAOC,IACfZ,OAAQA,EAAOC,KACfwB,OAAQuE,KAAKvE,OACbG,QAAS,IAAKoE,KAAKpE,UACnB,GAGR,CAEAoE,KAAKrD,oBAAqB,EAC1BqD,KAAKtC,aAAc,EAGnBmD,MAAM,GAAGb,KAAK/C,mBAAmB6D,6CACnC,EAEA/D,oBAAAA,GAEE8D,MAAM,mDACR,EAEAzG,UAAAA,CAAW2G,GACT,MAAMnF,EAAU,CAAEoF,IAAK,UAAWC,MAAO,UAAWC,KAAM,WAC1D,OAAO,IAAIV,KAAKO,GAAYI,mBAAmB,QAASvF,EAC1D,EAEAgD,aAAAA,GACE,OAAQwC,KAAKC,MAAsB,GAAhBD,KAAKE,UAAiB,IAAIC,UAC/C,EAEA1C,gBAAAA,GACE,OAAQuC,KAAKC,MAAsB,IAAhBD,KAAKE,UAAmB,IAAM,IAAIxC,QAAQ,EAC/D,EAEAC,eAAAA,GACE,MAAMyC,EAAU,CACd,cACA,mBACA,uBACA,aACA,wBAEF,OAAOA,EAAQJ,KAAKC,MAAMD,KAAKE,SAAWE,EAAQV,QACpD,I,UC7YJ,MAAMW,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://projet_gestion_scolaire/./src/views/bulletins/BulletinsGeneration.vue", "webpack://projet_gestion_scolaire/./src/views/bulletins/BulletinsGeneration.vue?87c4"], "sourcesContent": ["<template>\n  <div class=\"bulletins-container\">\n    <h1>Génération des Bulletins</h1>\n    \n    <div class=\"card\">\n      <h2>Paramètres de génération</h2>\n      <div class=\"generation-form\">\n        <div class=\"form-group\">\n          <label for=\"examen\">Examen</label>\n          <select id=\"examen\" v-model=\"selectedExamenId\" required>\n            <option value=\"\" disabled>Sélectionner un examen</option>\n            <option v-for=\"examen in examens\" :key=\"examen.id\" :value=\"examen.id\">\n              {{ examen.type }} ({{ formatDate(examen.dateDebut) }} - {{ formatDate(examen.dateFin) }})\n            </option>\n          </select>\n        </div>\n        \n        <div class=\"form-group\">\n          <label for=\"classe\">Classe</label>\n          <select id=\"classe\" v-model=\"selectedClasseId\" :disabled=\"!selectedExamenId\" required>\n            <option value=\"\" disabled>Sélectionner une classe</option>\n            <option v-for=\"classe in classesForExamen\" :key=\"classe.id\" :value=\"classe.id\">\n              {{ classe.nom }} ({{ classe.effectif }} élèves)\n            </option>\n          </select>\n        </div>\n        \n        <div class=\"form-group\">\n          <label for=\"eleve\">Élève (optionnel)</label>\n          <select id=\"eleve\" v-model=\"selectedEleveId\" :disabled=\"!selectedClasseId\">\n            <option value=\"\">Tous les élèves</option>\n            <option v-for=\"eleve in elevesForClasse\" :key=\"eleve.id\" :value=\"eleve.id\">\n              {{ eleve.nom }} {{ eleve.prenom }} ({{ eleve.matricule }})\n            </option>\n          </select>\n        </div>\n        \n        <div class=\"form-group\">\n          <label for=\"format\">Format</label>\n          <select id=\"format\" v-model=\"format\" required>\n            <option value=\"A5\">A5 Portrait</option>\n            <option value=\"A4\">A4 Portrait</option>\n          </select>\n        </div>\n        \n        <div class=\"form-group\">\n          <label>Options</label>\n          <div class=\"checkbox-item\">\n            <input type=\"checkbox\" id=\"inclureSignature\" v-model=\"options.inclureSignature\">\n            <label for=\"inclureSignature\">Inclure signature du directeur</label>\n          </div>\n          <div class=\"checkbox-item\">\n            <input type=\"checkbox\" id=\"inclureGraphique\" v-model=\"options.inclureGraphique\">\n            <label for=\"inclureGraphique\">Inclure graphique de progression</label>\n          </div>\n          <div class=\"checkbox-item\">\n            <input type=\"checkbox\" id=\"inclureAppreciation\" v-model=\"options.inclureAppreciation\">\n            <label for=\"inclureAppreciation\">Inclure appréciation générale</label>\n          </div>\n        </div>\n        \n        <div class=\"form-actions\">\n          <button @click=\"previewBulletin\" class=\"btn btn-secondary\" :disabled=\"!canGenerateBulletin\">\n            <i class=\"fas fa-eye\"></i> Aperçu\n          </button>\n          <button @click=\"generateBulletins\" class=\"btn btn-primary\" :disabled=\"!canGenerateBulletin\">\n            <i class=\"fas fa-file-pdf\"></i> Générer PDF\n          </button>\n        </div>\n      </div>\n    </div>\n    \n    <div v-if=\"bulletinsGenerated\" class=\"card mt-4\">\n      <div class=\"card-header-actions\">\n        <h2>Bulletins générés</h2>\n        <div class=\"header-actions\">\n          <button @click=\"downloadAllBulletins\" class=\"btn btn-primary\">\n            <i class=\"fas fa-download\"></i> Télécharger tout\n          </button>\n        </div>\n      </div>\n      \n      <div class=\"bulletins-list\">\n        <div v-for=\"(bulletin, index) in generatedBulletins\" :key=\"index\" class=\"bulletin-item\">\n          <div class=\"bulletin-info\">\n            <span class=\"bulletin-name\">{{ bulletin.eleveName }}</span>\n            <span class=\"bulletin-details\">{{ bulletin.classe }} - {{ bulletin.examen }}</span>\n          </div>\n          <div class=\"bulletin-actions\">\n            <button class=\"btn-icon\" title=\"Aperçu\">\n              <i class=\"fas fa-eye\"></i>\n            </button>\n            <button class=\"btn-icon\" title=\"Télécharger\">\n              <i class=\"fas fa-download\"></i>\n            </button>\n            <button class=\"btn-icon\" title=\"Imprimer\">\n              <i class=\"fas fa-print\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <div v-if=\"showPreview\" class=\"bulletin-preview-overlay\">\n      <div class=\"bulletin-preview\">\n        <div class=\"preview-header\">\n          <h3>Aperçu du bulletin</h3>\n          <button @click=\"closePreview\" class=\"btn-close\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        <div class=\"preview-content\">\n          <div class=\"bulletin-template\" :class=\"format\">\n            <div class=\"bulletin-header\">\n              <div class=\"school-info\">\n                <h2>Collège Privé Adventiste Avaratetezana</h2>\n                <p>Année scolaire: 2024-2025</p>\n                <p>{{ currentExamen ? currentExamen.type : '' }}</p>\n              </div>\n              <div class=\"student-info\">\n                <p><strong>Nom et prénom:</strong> {{ currentEleve ? `${currentEleve.nom} ${currentEleve.prenom}` : 'Dupont Jean' }}</p>\n                <p><strong>Matricule:</strong> {{ currentEleve ? currentEleve.matricule : 'E001' }}</p>\n                <p><strong>Classe:</strong> {{ currentClasse ? currentClasse.nom : '6ème A' }}</p>\n                <p><strong>Effectif:</strong> {{ currentClasse ? currentClasse.effectif : '35' }}</p>\n              </div>\n            </div>\n            \n            <div class=\"bulletin-body\">\n              <table class=\"grades-table\">\n                <thead>\n                  <tr>\n                    <th>Matière (Coeff)</th>\n                    <th>DS1 (/20)</th>\n                    <th>DS2 (/20)</th>\n                    <th>Examen (/20)</th>\n                    <th>Moyenne (/20)</th>\n                    <th>Total Pondéré</th>\n                    <th>Remarques</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr v-for=\"(matiere, index) in matieres\" :key=\"index\">\n                    <td>{{ matiere.nom }} ({{ matiere.coefficient }})</td>\n                    <td>{{ getRandomNote() }}</td>\n                    <td>{{ getRandomNote() }}</td>\n                    <td>{{ getRandomNote() }}</td>\n                    <td>{{ getRandomAverage() }}</td>\n                    <td>{{ (getRandomAverage() * matiere.coefficient).toFixed(1) }}</td>\n                    <td>{{ getRandomRemark() }}</td>\n                  </tr>\n                </tbody>\n              </table>\n              \n              <div class=\"bulletin-summary\">\n                <div class=\"summary-item\">\n                  <span>Somme des Totaux Pondérés:</span>\n                  <span>287.5</span>\n                </div>\n                <div class=\"summary-item\">\n                  <span>Somme des Coefficients:</span>\n                  <span>18</span>\n                </div>\n                <div class=\"summary-item\">\n                  <span>Moyenne Générale Élève:</span>\n                  <span>15.97/20</span>\n                </div>\n                <div class=\"summary-item\">\n                  <span>Moyenne de la Classe:</span>\n                  <span>14.23/20</span>\n                </div>\n                <div class=\"summary-item\">\n                  <span>Rang:</span>\n                  <span>5ème/35</span>\n                </div>\n              </div>\n              \n              <div v-if=\"options.inclureGraphique\" class=\"bulletin-chart\">\n                <p class=\"chart-title\">Progression trimestrielle</p>\n                <div class=\"chart-placeholder\">\n                  [Graphique de progression]\n                </div>\n              </div>\n              \n              <div v-if=\"options.inclureAppreciation\" class=\"bulletin-appreciation\">\n                <p class=\"appreciation-title\">Appréciation générale</p>\n                <p class=\"appreciation-content\">Élève sérieux et appliqué. Bons résultats dans l'ensemble. Poursuivez vos efforts.</p>\n              </div>\n            </div>\n            \n            <div class=\"bulletin-footer\">\n              <div class=\"signature-block\">\n                <div class=\"signature-item\">\n                  <p>Professeur Principal</p>\n                  <div class=\"signature-line\"></div>\n                </div>\n                <div v-if=\"options.inclureSignature\" class=\"signature-item\">\n                  <p>Directrice</p>\n                  <div class=\"signature-line\"></div>\n                </div>\n                <div v-if=\"options.inclureSignature\" class=\"signature-item\">\n                  <p>Cachet de l'établissement</p>\n                  <div class=\"signature-stamp\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"preview-actions\">\n          <button @click=\"closePreview\" class=\"btn btn-secondary\">Fermer</button>\n          <button @click=\"generateBulletins\" class=\"btn btn-primary\">\n            <i class=\"fas fa-file-pdf\"></i> Générer PDF\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'BulletinsGeneration',\n  data() {\n    return {\n      selectedExamenId: '',\n      selectedClasseId: '',\n      selectedEleveId: '',\n      format: 'A5',\n      options: {\n        inclureSignature: true,\n        inclureGraphique: false,\n        inclureAppreciation: true\n      },\n      \n      examens: [\n        { \n          id: 1, \n          type: 'Trimestre 1', \n          dateDebut: '2024-09-15', \n          dateFin: '2024-10-30', \n          classesIds: [1, 2], \n          verrouille: true \n        },\n        { \n          id: 2, \n          type: 'Trimestre 1', \n          dateDebut: '2024-09-15', \n          dateFin: '2024-10-30', \n          classesIds: [3, 4], \n          verrouille: false \n        }\n      ],\n      \n      classes: [\n        { id: 1, nom: '6ème A', effectif: 35 },\n        { id: 2, nom: '6ème B', effectif: 32 },\n        { id: 3, nom: '5ème A', effectif: 30 },\n        { id: 4, nom: '5ème B', effectif: 28 }\n      ],\n      \n      eleves: [\n        { id: 1, nom: 'Dupont', prenom: 'Jean', matricule: 'E001', classeId: 1 },\n        { id: 2, nom: 'Martin', prenom: 'Sophie', matricule: 'E002', classeId: 1 },\n        { id: 3, nom: 'Dubois', prenom: 'Pierre', matricule: 'E003', classeId: 1 },\n        { id: 4, nom: 'Lefebvre', prenom: 'Marie', matricule: 'E004', classeId: 2 },\n        { id: 5, nom: 'Moreau', prenom: 'Lucas', matricule: 'E005', classeId: 2 }\n      ],\n      \n      matieres: [\n        { id: 1, nom: 'Mathématiques', abreviation: 'MATH', coefficient: 4 },\n        { id: 2, nom: 'Physique', abreviation: 'PHY', coefficient: 3 },\n        { id: 3, nom: 'Français', abreviation: 'FR', coefficient: 3 },\n        { id: 4, nom: 'Histoire-Géographie', abreviation: 'HIST', coefficient: 2 },\n        { id: 5, nom: 'Anglais', abreviation: 'ANG', coefficient: 2 },\n        { id: 6, nom: 'SVT', abreviation: 'SVT', coefficient: 2 },\n        { id: 7, nom: 'Éducation Physique', abreviation: 'EPS', coefficient: 1 },\n        { id: 8, nom: 'Arts Plastiques', abreviation: 'ART', coefficient: 1 }\n      ],\n      \n      showPreview: false,\n      bulletinsGenerated: false,\n      generatedBulletins: []\n    }\n  },\n  \n  computed: {\n    classesForExamen() {\n      if (!this.selectedExamenId) return [];\n      \n      const examen = this.examens.find(e => e.id === this.selectedExamenId);\n      if (!examen) return [];\n      \n      return this.classes.filter(c => examen.classesIds.includes(c.id));\n    },\n    \n    elevesForClasse() {\n      if (!this.selectedClasseId) return [];\n      return this.eleves.filter(e => e.classeId === this.selectedClasseId);\n    },\n    \n    canGenerateBulletin() {\n      return this.selectedExamenId && this.selectedClasseId;\n    },\n    \n    currentExamen() {\n      return this.examens.find(e => e.id === this.selectedExamenId);\n    },\n    \n    currentClasse() {\n      return this.classes.find(c => c.id === this.selectedClasseId);\n    },\n    \n    currentEleve() {\n      if (!this.selectedEleveId) return null;\n      return this.eleves.find(e => e.id === this.selectedEleveId);\n    }\n  },\n  \n  methods: {\n    previewBulletin() {\n      this.showPreview = true;\n    },\n    \n    closePreview() {\n      this.showPreview = false;\n    },\n    \n    generateBulletins() {\n      // Dans une application réelle, on générerait les PDF et les enverrait au serveur\n      // Pour l'instant, on simule la génération\n      \n      this.generatedBulletins = [];\n      \n      if (this.selectedEleveId) {\n        // Génération pour un seul élève\n        const eleve = this.eleves.find(e => e.id === this.selectedEleveId);\n        const classe = this.classes.find(c => c.id === this.selectedClasseId);\n        const examen = this.examens.find(e => e.id === this.selectedExamenId);\n        \n        if (eleve && classe && examen) {\n          this.generatedBulletins.push({\n            id: Date.now(),\n            eleveId: eleve.id,\n            eleveName: `${eleve.nom} ${eleve.prenom}`,\n            classe: classe.nom,\n            examen: examen.type,\n            format: this.format,\n            options: { ...this.options }\n          });\n        }\n      } else {\n        // Génération pour toute la classe\n        const classe = this.classes.find(c => c.id === this.selectedClasseId);\n        const examen = this.examens.find(e => e.id === this.selectedExamenId);\n        const classEleves = this.eleves.filter(e => e.classeId === this.selectedClasseId);\n        \n        if (classe && examen) {\n          classEleves.forEach(eleve => {\n            this.generatedBulletins.push({\n              id: Date.now() + eleve.id,\n              eleveId: eleve.id,\n              eleveName: `${eleve.nom} ${eleve.prenom}`,\n              classe: classe.nom,\n              examen: examen.type,\n              format: this.format,\n              options: { ...this.options }\n            });\n          });\n        }\n      }\n      \n      this.bulletinsGenerated = true;\n      this.showPreview = false;\n      \n      // Afficher un message de succès\n      alert(`${this.generatedBulletins.length} bulletin(s) généré(s) avec succès !`);\n    },\n    \n    downloadAllBulletins() {\n      // Dans une application réelle, on téléchargerait les PDF\n      alert('Téléchargement de tous les bulletins en cours...');\n    },\n    \n    formatDate(dateString) {\n      const options = { day: '2-digit', month: '2-digit', year: 'numeric' };\n      return new Date(dateString).toLocaleDateString('fr-FR', options);\n    },\n    \n    getRandomNote() {\n      return (Math.floor(Math.random() * 10) + 10).toString();\n    },\n    \n    getRandomAverage() {\n      return (Math.floor(Math.random() * 1000) / 100 + 10).toFixed(2);\n    },\n    \n    getRandomRemark() {\n      const remarks = [\n        'Bon travail',\n        'Peut mieux faire',\n        'Excellents résultats',\n        'En progrès',\n        'Efforts à poursuivre'\n      ];\n      return remarks[Math.floor(Math.random() * remarks.length)];\n    }\n  }\n}\n</script>\n\n<style scoped>\n.bulletins-container {\n  padding: 1.5rem;\n}\n\nh1 {\n  margin-bottom: 1.5rem;\n  color: #333;\n}\n\n.card {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\nh2 {\n  margin-top: 0;\n  margin-bottom: 1rem;\n  font-size: 1.2rem;\n  color: #333;\n  border-bottom: 1px solid #eee;\n  padding-bottom: 0.5rem;\n}\n\n.generation-form {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 1rem;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\nlabel {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #555;\n}\n\ninput, select {\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 1rem;\n}\n\n.checkbox-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.checkbox-item input[type=\"checkbox\"] {\n  width: auto;\n  margin-right: 0.5rem;\n}\n\n.form-actions {\n  grid-column: 1 / -1;\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  margin-top: 1rem;\n}\n\n.btn {\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: background-color 0.3s;\n  display: flex;\n  align-items: center;\n}\n\n.btn i {\n  margin-right: 0.5rem;\n}\n\n.btn-primary {\n  background-color: #3f51b5;\n  color: white;\n}\n\n.btn-primary:hover {\n  background-color: #303f9f;\n}\n\n.btn-secondary {\n  background-color: #f5f5f5;\n  color: #333;\n}\n\n.btn-secondary:hover {\n  background-color: #e0e0e0;\n}\n\n.btn:disabled {\n  background-color: #cccccc;\n  cursor: not-allowed;\n}\n\n.mt-4 {\n  margin-top: 1.5rem;\n}\n\n.card-header-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n  border-bottom: 1px solid #eee;\n  padding-bottom: 0.5rem;\n}\n\n.card-header-actions h2 {\n  margin: 0;\n  border-bottom: none;\n  padding-bottom: 0;\n}\n\n.header-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.bulletins-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 1rem;\n}\n\n.bulletin-item {\n  border: 1px solid #eee;\n  border-radius: 4px;\n  padding: 1rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.bulletin-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.bulletin-name {\n  font-weight: 500;\n  margin-bottom: 0.25rem;\n}\n\n.bulletin-details {\n  font-size: 0.8rem;\n  color: #757575;\n}\n\n.bulletin-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.btn-icon {\n  background: none;\n  border: none;\n  color: #3f51b5;\n  cursor: pointer;\n  font-size: 1rem;\n  padding: 0.25rem;\n}\n\n.btn-icon:hover {\n  color: #303f9f;\n}\n\n/* Bulletin Preview Styles */\n.bulletin-preview-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n.bulletin-preview {\n  background-color: white;\n  border-radius: 8px;\n  width: 90%;\n  max-width: 900px;\n  max-height: 90vh;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  display: flex;\n  flex-direction: column;\n}\n\n.preview-header {\n  padding: 1rem;\n  border-bottom: 1px solid #eee;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.preview-header h3 {\n  margin: 0;\n  font-size: 1.2rem;\n}\n\n.btn-close {\n  background: none;\n  border: none;\n  font-size: 1.2rem;\n  cursor: pointer;\n  color: #757575;\n}\n\n.preview-content {\n  padding: 1rem;\n  overflow-y: auto;\n  flex-grow: 1;\n}\n\n.preview-actions {\n  padding: 1rem;\n  border-top: 1px solid #eee;\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n}\n\n/* Bulletin Template Styles */\n.bulletin-template {\n  background-color: white;\n  border: 1px solid #ddd;\n  margin: 0 auto;\n  padding: 1.5rem;\n  font-size: 0.9rem;\n}\n\n.bulletin-template.A5 {\n  width: 148mm;\n  height: 210mm;\n  max-width: 100%;\n}\n\n.bulletin-template.A4 {\n  width: 210mm;\n  height: 297mm;\n  max-width: 100%;\n}\n\n.bulletin-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 1.5rem;\n  border-bottom: 2px solid #3f51b5;\n  padding-bottom: 1rem;\n}\n\n.school-info h2 {\n  margin: 0 0 0.5rem 0;\n  font-size: 1.2rem;\n  color: #3f51b5;\n  border-bottom: none;\n}\n\n.school-info p {\n  margin: 0.25rem 0;\n}\n\n.student-info p {\n  margin: 0.25rem 0;\n}\n\n.bulletin-body {\n  margin-bottom: 1.5rem;\n}\n\n.grades-table {\n  width: 100%;\n  border-collapse: collapse;\n  margin-bottom: 1.5rem;\n  font-size: 0.8rem;\n}\n\n.grades-table th, .grades-table td {\n  border: 1px solid #ddd;\n  padding: 0.5rem;\n  text-align: center;\n}\n\n.grades-table th {\n  background-color: #f5f5f5;\n  font-weight: 500;\n}\n\n.grades-table td:first-child {\n  text-align: left;\n}\n\n.bulletin-summary {\n  margin-bottom: 1.5rem;\n}\n\n.summary-item {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 0.5rem;\n  padding: 0.5rem;\n  background-color: #f9f9f9;\n}\n\n.bulletin-chart, .bulletin-appreciation {\n  margin-bottom: 1.5rem;\n  padding: 0.5rem;\n  border: 1px solid #eee;\n}\n\n.chart-title, .appreciation-title {\n  font-weight: 500;\n  margin-bottom: 0.5rem;\n  color: #3f51b5;\n}\n\n.chart-placeholder {\n  height: 100px;\n  background-color: #f5f5f5;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  color: #757575;\n}\n\n.bulletin-footer {\n  border-top: 2px solid #3f51b5;\n  padding-top: 1rem;\n}\n\n.signature-block {\n  display: flex;\n  justify-content: space-between;\n}\n\n.signature-item {\n  text-align: center;\n  width: 30%;\n}\n\n.signature-line {\n  height: 1px;\n  background-color: #ddd;\n  margin-top: 2rem;\n}\n\n.signature-stamp {\n  width: 80px;\n  height: 80px;\n  border: 1px dashed #ddd;\n  border-radius: 50%;\n  margin: 0.5rem auto;\n}\n</style>\n", "import { render } from \"./BulletinsGeneration.vue?vue&type=template&id=52310e90&scoped=true\"\nimport script from \"./BulletinsGeneration.vue?vue&type=script&lang=js\"\nexport * from \"./BulletinsGeneration.vue?vue&type=script&lang=js\"\n\nimport \"./BulletinsGeneration.vue?vue&type=style&index=0&id=52310e90&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-52310e90\"]])\n\nexport default __exports__"], "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "for", "id", "_cache", "$event", "$data", "selectedExamenId", "required", "value", "disabled", "_Fragment", "_renderList", "examens", "examen", "type", "_toDisplayString", "$options", "formatDate", "dateDebut", "dateFin", "_hoisted_5", "_hoisted_6", "selectedClasseId", "classesForExamen", "classe", "nom", "effectif", "_hoisted_8", "_hoisted_7", "_hoisted_9", "selectedEleveId", "elevesForClasse", "eleve", "prenom", "matricule", "_hoisted_11", "_hoisted_10", "_hoisted_12", "format", "_hoisted_13", "_hoisted_14", "options", "inclureSignature", "_hoisted_15", "inclureGraphique", "_hoisted_16", "inclureAppreciation", "_hoisted_17", "onClick", "args", "previewBulletin", "canGenerateBulletin", "_createTextVNode", "_hoisted_18", "generateBulletins", "_hoisted_19", "bulletinsGenerated", "_hoisted_20", "_hoisted_21", "_hoisted_22", "downloadAllBulletins", "_hoisted_23", "generatedBulletins", "bulletin", "index", "_hoisted_24", "_hoisted_25", "eleveName", "_hoisted_26", "_createStaticVNode", "_createCommentVNode", "showPreview", "_hoisted_27", "_hoisted_28", "_hoisted_29", "closePreview", "_hoisted_30", "_normalizeClass", "_hoisted_31", "_hoisted_32", "currentExamen", "_hoisted_33", "currentEleve", "currentClasse", "_hoisted_34", "_hoisted_35", "matieres", "matiere", "coefficient", "getRandomNote", "getRandomAverage", "toFixed", "getRandomRemark", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "name", "data", "classesIds", "<PERSON><PERSON><PERSON><PERSON>", "classes", "eleves", "classeId", "abreviation", "computed", "this", "find", "e", "filter", "c", "includes", "methods", "push", "Date", "now", "eleveId", "classEleves", "for<PERSON>ach", "alert", "length", "dateString", "day", "month", "year", "toLocaleDateString", "Math", "floor", "random", "toString", "remarks", "__exports__", "render"], "sourceRoot": ""}