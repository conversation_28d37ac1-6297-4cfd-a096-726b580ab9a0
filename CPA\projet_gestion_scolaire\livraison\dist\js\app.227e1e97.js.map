{"version": 3, "file": "js/app.227e1e97.js", "mappings": "0ZACOA,MAAM,O,GAEJA,MAAM,kB,GAEHA,MAAM,W,0IAJhBC,EAAAA,EAAAA,IASM,MATNC,EASM,EARJC,EAAAA,EAAAA,IAAUC,IACVC,EAAAA,EAAAA,IAKM,MALNC,EAKM,EAJJH,EAAAA,EAAAA,IAAWI,IACXF,EAAAA,EAAAA,IAEO,OAFPG,EAEO,EADLL,EAAAA,EAAAA,IAAeM,QAGnBN,EAAAA,EAAAA,IAAUO,I,UCRJV,MAAM,c,GACPA,MAAM,kB,0CADbC,EAAAA,EAAAA,IASS,SATTC,EASS,EARPG,EAAAA,EAAAA,IAGM,MAHNC,EAGM,EALVK,EAAAA,EAAAA,IAAA,oBAIMN,EAAAA,EAAAA,IAA+C,UAA3C,0CAAsC,mBAE5CA,EAAAA,EAAAA,IAGM,OAHDL,MAAM,aAAW,EACpBK,EAAAA,EAAAA,IAA6C,QAAvCL,MAAM,aAAY,mBACxBK,EAAAA,EAAAA,IAA+C,UAAvCL,MAAM,cAAa,iBAAW,K,CAM5C,OACEY,KAAM,a,UCRR,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,Q,SCROb,MAAM,W,GAIJA,MAAM,e,GAOHA,MAAM,gB,GAoBNA,MAAM,gB,GAyBNA,MAAM,gB,0EAxDhBC,EAAAA,EAAAA,IAyEM,MAzENC,EAyEM,gBAxEJG,EAAAA,EAAAA,IAEM,OAFDL,MAAM,kBAAgB,EACzBK,EAAAA,EAAAA,IAAuB,UAAnB,oBAAc,KAEpBA,EAAAA,EAAAA,IAoEM,MApENC,EAoEM,EAnEJD,EAAAA,EAAAA,IAkEK,YAjEHA,EAAAA,EAAAA,IAIK,YAHHF,EAAAA,EAAAA,IAEcW,EAAA,CAFDC,GAAG,IAAIC,MAAA,I,CAR9BC,SAAAC,EAAAA,EAAAA,KASY,IAA2BC,EAAA,KAAAA,EAAA,KAA3Bd,EAAAA,EAAAA,IAA2B,KAAxBL,MAAM,eAAa,UATlCoB,EAAAA,EAAAA,IASuC,yBATvCC,EAAA,OAYQhB,EAAAA,EAAAA,IAmBK,KAnBLG,EAmBK,cAlBHH,EAAAA,EAAAA,IAA0C,QAApCL,MAAM,iBAAgB,WAAO,KACnCK,EAAAA,EAAAA,IAgBK,YAfHA,EAAAA,EAAAA,IAIK,YAHHF,EAAAA,EAAAA,IAEcW,EAAA,CAFDC,GAAG,0BAAwB,CAhBtDE,SAAAC,EAAAA,EAAAA,KAiBgB,IAAsCC,EAAA,KAAAA,EAAA,KAAtCd,EAAAA,EAAAA,IAAsC,KAAnCL,MAAM,0BAAwB,UAjBjDoB,EAAAA,EAAAA,IAiBsD,uBAjBtDC,EAAA,OAoBYhB,EAAAA,EAAAA,IAIK,YAHHF,EAAAA,EAAAA,IAEcW,EAAA,CAFDC,GAAG,0BAAwB,CArBtDE,SAAAC,EAAAA,EAAAA,KAsBgB,IAAmCC,EAAA,KAAAA,EAAA,KAAnCd,EAAAA,EAAAA,IAAmC,KAAhCL,MAAM,uBAAqB,UAtB9CoB,EAAAA,EAAAA,IAsBmD,uBAtBnDC,EAAA,OAyBYhB,EAAAA,EAAAA,IAIK,YAHHF,EAAAA,EAAAA,IAEcW,EAAA,CAFDC,GAAG,qBAAmB,CA1BjDE,SAAAC,EAAAA,EAAAA,KA2BgB,IAAgCC,EAAA,KAAAA,EAAA,KAAhCd,EAAAA,EAAAA,IAAgC,KAA7BL,MAAM,oBAAkB,UA3B3CoB,EAAAA,EAAAA,IA2BgD,kBA3BhDC,EAAA,WAgCQhB,EAAAA,EAAAA,IAwBK,KAxBLiB,EAwBK,cAvBHjB,EAAAA,EAAAA,IAA6C,QAAvCL,MAAM,iBAAgB,cAAU,KACtCK,EAAAA,EAAAA,IAqBK,YApBHA,EAAAA,EAAAA,IAIK,YAHHF,EAAAA,EAAAA,IAEcW,EAAA,CAFDC,GAAG,aAAW,CApCzCE,SAAAC,EAAAA,EAAAA,KAqCgB,IAA2BC,EAAA,KAAAA,EAAA,KAA3Bd,EAAAA,EAAAA,IAA2B,KAAxBL,MAAM,eAAa,UArCtCoB,EAAAA,EAAAA,IAqC2C,kBArC3CC,EAAA,OAwCYhB,EAAAA,EAAAA,IAIK,YAHHF,EAAAA,EAAAA,IAEcW,EAAA,CAFDC,GAAG,YAAU,CAzCxCE,SAAAC,EAAAA,EAAAA,KA0CgB,IAA4BC,EAAA,KAAAA,EAAA,KAA5Bd,EAAAA,EAAAA,IAA4B,KAAzBL,MAAM,gBAAc,UA1CvCoB,EAAAA,EAAAA,IA0C4C,iBA1C5CC,EAAA,OA6CYhB,EAAAA,EAAAA,IAIK,YAHHF,EAAAA,EAAAA,IAEcW,EAAA,CAFDC,GAAG,UAAQ,CA9CtCE,SAAAC,EAAAA,EAAAA,KA+CgB,IAA0BC,EAAA,KAAAA,EAAA,KAA1Bd,EAAAA,EAAAA,IAA0B,KAAvBL,MAAM,cAAY,UA/CrCoB,EAAAA,EAAAA,IA+C0C,0BA/C1CC,EAAA,OAkDYhB,EAAAA,EAAAA,IAIK,YAHHF,EAAAA,EAAAA,IAEcW,EAAA,CAFDC,GAAG,cAAY,CAnD1CE,SAAAC,EAAAA,EAAAA,KAoDgB,IAA+BC,EAAA,KAAAA,EAAA,KAA/Bd,EAAAA,EAAAA,IAA+B,KAA5BL,MAAM,mBAAiB,UApD1CoB,EAAAA,EAAAA,IAoD+C,mBApD/CC,EAAA,WAyDQhB,EAAAA,EAAAA,IAcK,KAdLkB,EAcK,gBAbHlB,EAAAA,EAAAA,IAA2C,QAArCL,MAAM,iBAAgB,YAAQ,KACpCK,EAAAA,EAAAA,IAWK,YAVHA,EAAAA,EAAAA,IAIK,YAHHF,EAAAA,EAAAA,IAEcW,EAAA,CAFDC,GAAG,aAAW,CA7DzCE,SAAAC,EAAAA,EAAAA,KA8DgB,IAA8BC,EAAA,MAAAA,EAAA,MAA9Bd,EAAAA,EAAAA,IAA8B,KAA3BL,MAAM,kBAAgB,UA9DzCoB,EAAAA,EAAAA,IA8D8C,kBA9D9CC,EAAA,OAiEYhB,EAAAA,EAAAA,IAIK,YAHHF,EAAAA,EAAAA,IAEcW,EAAA,CAFDC,GAAG,iBAAe,CAlE7CE,SAAAC,EAAAA,EAAAA,KAmEgB,IAAiCC,EAAA,MAAAA,EAAA,MAAjCd,EAAAA,EAAAA,IAAiC,KAA9BL,MAAM,qBAAmB,UAnE5CoB,EAAAA,EAAAA,IAmEiD,sBAnEjDC,EAAA,e,CA8EA,OACET,KAAM,cCxER,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,Q,mBCRUZ,MAAM,c,GACPA,MAAM,kB,0CADbC,EAAAA,EAAAA,IAKS,SALTC,EAKS,EAJPG,EAAAA,EAAAA,IAGM,MAHNC,EAGM,EAFJD,EAAAA,EAAAA,IAA6F,SAA1F,MAAOmB,EAAAA,EAAAA,IAAGC,EAAAC,aAAc,iEAA8D,gBACzFrB,EAAAA,EAAAA,IAAoC,KAAjCL,MAAM,WAAU,iBAAa,O,CAMtC,OACEY,KAAM,YACNe,SAAU,CACRD,WAAAA,GACE,OAAO,IAAIE,MAAOC,aACpB,ICRJ,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,QNSA,GACEjB,KAAM,MACNkB,WAAY,CACVC,OAAM,EACNC,QAAO,EACPC,OAAMA,GAERC,OAAAA,GAEEC,KAAKC,OAAOC,SAAS,eACrBF,KAAKC,OAAOC,SAAS,gBACrBF,KAAKC,OAAOC,SAAS,iBACrBF,KAAKC,OAAOC,SAAS,eACvB,GOxBF,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,KAEpE,Q,UCNAC,EAAAA,WAAIC,IAAIC,EAAAA,YAGR,MAAMC,EAAYA,IAAM,8BAClBC,EAAsBA,IAAM,sDAC5BC,EAAsBA,IAAM,qDAC5BC,EAAkBA,IAAM,sDACxBC,EAAeA,IAAM,sDACrBC,EAAcA,IAAM,sDACpBC,EAAeA,IAAM,sDACrBC,EAAsBA,IAAM,sDAC5BC,EAAoBA,IAAM,sDAC1BC,EAAwBA,IAAM,qDAE9BC,EAAS,CACb,CACEC,KAAM,IACNzC,KAAM,YACN0C,UAAWZ,GAGb,CACEW,KAAM,yBACNzC,KAAM,sBACN0C,UAAWX,GAEb,CACEU,KAAM,yBACNzC,KAAM,sBACN0C,UAAWV,GAEb,CACES,KAAM,oBACNzC,KAAM,kBACN0C,UAAWT,GAGb,CACEQ,KAAM,YACNzC,KAAM,eACN0C,UAAWR,GAEb,CACEO,KAAM,WACNzC,KAAM,cACN0C,UAAWP,GAGb,CACEM,KAAM,SACNzC,KAAM,eACN0C,UAAWN,GAGb,CACEK,KAAM,aACNzC,KAAM,sBACN0C,UAAWL,GAGb,CACEI,KAAM,YACNzC,KAAM,oBACN0C,UAAWJ,GAEb,CACEG,KAAM,gBACNzC,KAAM,wBACN0C,UAAWH,GAGb,CACEE,KAAM,IACNE,SAAU,MAIRC,EAAS,IAAIf,EAAAA,WAAU,CAC3BgB,KAAM,UACNC,KAAMC,IACNP,WAGF,Q,0HCpFA,MAAMQ,EAASC,GAAO,IAAIC,SAAQC,GAAWC,WAAWD,EAASF,KAGpDI,EAAgB,CAC3B,YAAMC,GAEJ,aADMN,EAAM,KACL,CACL,CAAEO,GAAI,EAAGC,IAAK,SAAUC,OAAQ,OAAQC,UAAW,OAAQC,SAAU,GACrE,CAAEJ,GAAI,EAAGC,IAAK,SAAUC,OAAQ,SAAUC,UAAW,OAAQC,SAAU,GACvE,CAAEJ,GAAI,EAAGC,IAAK,SAAUC,OAAQ,SAAUC,UAAW,OAAQC,SAAU,GACvE,CAAEJ,GAAI,EAAGC,IAAK,WAAYC,OAAQ,QAASC,UAAW,OAAQC,SAAU,GACxE,CAAEJ,GAAI,EAAGC,IAAK,SAAUC,OAAQ,QAASC,UAAW,OAAQC,SAAU,GACtE,CAAEJ,GAAI,EAAGC,IAAK,QAASC,OAAQ,OAAQC,UAAW,OAAQC,SAAU,GACpE,CAAEJ,GAAI,EAAGC,IAAK,SAAUC,OAAQ,SAAUC,UAAW,OAAQC,SAAU,GAE3E,EAEA,iBAAMC,CAAYD,GAChB,MAAME,QAAetC,KAAK+B,SAC1B,OAAOO,EAAOC,QAAOC,GAASA,EAAMJ,WAAaA,GACnD,EAEA,aAAMK,CAAQT,GACZ,MAAMM,QAAetC,KAAK+B,SAC1B,OAAOO,EAAOI,MAAKF,GAASA,EAAMR,KAAOA,GAC3C,GAIWW,EAAiB,CAC5B,YAAMZ,GAEJ,aADMN,EAAM,KACL,CACL,CAAEO,GAAI,EAAGC,IAAK,SAAUW,SAAU,IAClC,CAAEZ,GAAI,EAAGC,IAAK,SAAUW,SAAU,IAClC,CAAEZ,GAAI,EAAGC,IAAK,SAAUW,SAAU,IAClC,CAAEZ,GAAI,EAAGC,IAAK,SAAUW,SAAU,IAEtC,EAEA,aAAMH,CAAQT,GACZ,MAAMa,QAAgB7C,KAAK+B,SAC3B,OAAOc,EAAQH,MAAKI,GAAUA,EAAOd,KAAOA,GAC9C,GAIWe,GAAkB,CAC7B,YAAMhB,GAEJ,aADMN,EAAM,KACL,CACL,CAAEO,GAAI,EAAGC,IAAK,gBAAiBe,YAAa,OAAQC,YAAa,GACjE,CAAEjB,GAAI,EAAGC,IAAK,WAAYe,YAAa,MAAOC,YAAa,GAC3D,CAAEjB,GAAI,EAAGC,IAAK,WAAYe,YAAa,KAAMC,YAAa,GAC1D,CAAEjB,GAAI,EAAGC,IAAK,sBAAuBe,YAAa,OAAQC,YAAa,GACvE,CAAEjB,GAAI,EAAGC,IAAK,UAAWe,YAAa,MAAOC,YAAa,GAC1D,CAAEjB,GAAI,EAAGC,IAAK,MAAOe,YAAa,MAAOC,YAAa,GACtD,CAAEjB,GAAI,EAAGC,IAAK,qBAAsBe,YAAa,MAAOC,YAAa,GACrE,CAAEjB,GAAI,EAAGC,IAAK,kBAAmBe,YAAa,MAAOC,YAAa,GAEtE,EAEA,YAAMC,CAAOC,GAGX,aAFM1B,EAAM,KAEL,IAAK0B,EAASnB,GAAIvC,KAAK2D,MAChC,EAEA,YAAMC,CAAOnE,EAAGiE,GAGd,aAFM1B,EAAM,KAEL,IAAK0B,EACd,EAEA,YAAMG,CAAOtB,GAGX,aAFMP,EAAM,KAEL,CAAE8B,SAAS,EACpB,GAIWC,GAAiB,CAC5B,YAAMzB,GAEJ,aADMN,EAAM,KACL,CACL,CACEO,GAAI,EACJyB,KAAM,cACNC,UAAW,aACXC,QAAS,aACTC,WAAY,CAAC,EAAG,GAChBC,YAAY,GAEd,CACE7B,GAAI,EACJyB,KAAM,cACNC,UAAW,aACXC,QAAS,aACTC,WAAY,CAAC,EAAG,GAChBC,YAAY,GAEd,CACE7B,GAAI,EACJyB,KAAM,cACNC,UAAW,aACXC,QAAS,aACTC,WAAY,CAAC,EAAG,EAAG,EAAG,GACtBC,YAAY,GAGlB,EAEA,YAAMX,CAAOY,GAGX,aAFMrC,EAAM,KAEL,IAAKqC,EAAQ9B,GAAIvC,KAAK2D,MAC/B,EAEA,YAAMC,CAAOrB,EAAI8B,GAGf,aAFMrC,EAAM,KAEL,IAAKqC,EAAQ9B,KACtB,EAEA,wBAAM+B,CAAmB/B,SACjBP,EAAM,KACZ,MAAMuC,QAAgBhE,KAAK+B,SACrB+B,EAASE,EAAQtB,MAAKuB,GAAKA,EAAEjC,KAAOA,IAC1C,GAAI8B,EAEF,OADAA,EAAOD,YAAcC,EAAOD,WACrBC,EAET,MAAM,IAAII,MAAM,oBAClB,GAIWC,GAAe,CAC1B,0BAAMC,CAAqBhC,EAAUlD,SAC7BuC,EAAM,KAGZ,MAAMa,QAAeR,EAAcO,YAAYD,GAGzCiC,QAAiBtB,GAAgBhB,SAGjCuC,EAAQ,CAAC,EAaf,OAXAhC,EAAOiC,SAAQ/B,IACb8B,EAAM9B,EAAMR,IAAM,CAAC,EACnBqC,EAASE,SAAQpB,IACfmB,EAAM9B,EAAMR,IAAImB,EAAQnB,IAAM,CAC5BwC,IAAKC,KAAKC,MAAsB,GAAhBD,KAAKE,UAAiB,GACtCC,IAAKH,KAAKC,MAAsB,GAAhBD,KAAKE,UAAiB,GACtCb,OAAQW,KAAKC,MAAsB,GAAhBD,KAAKE,UAAiB,GAC1C,GACD,IAGGL,CACT,EAEA,eAAMO,GAGJ,aAFMpD,EAAM,KAEL,CAAE8B,SAAS,EAAMuB,QAAS,iCACnC,EAEA,uBAAMC,CAAkB3C,EAAU4C,SAC1BvD,EAAM,KAGZ,MAAMa,QAAeR,EAAcO,YAAYD,GAGzCiC,QAAiBtB,GAAgBhB,SAGjCuC,QAActE,KAAKoE,qBAAqBhC,EAAU4C,GAGlDC,EAAW,CAAC,EACZC,EAAiB,CAAC,EAClBC,EAAoBd,EAASe,QAAO,CAACC,EAAKlC,IAAYkC,EAAMlC,EAAQF,aAAa,GAEvFX,EAAOiC,SAAQ/B,IACb,IAAI8C,EAAc,EAElBjB,EAASE,SAAQpB,IACf,MAAMoC,EAAajB,EAAM9B,EAAMR,IAAImB,EAAQnB,IACrCwD,GAAWC,WAAWF,EAAWf,KAAOiB,WAAWF,EAAWX,KAAOa,WAAWF,EAAWzB,SAAW,EACtG4B,EAASF,EAAUrC,EAAQF,YAEjCqC,GAAeI,CAAM,IAGvBT,EAASzC,EAAMR,IAAMsD,EAAcH,EACnCD,EAAe1C,EAAMR,IAAMsD,CAAW,IAIxC,MAAMK,EAAgBC,OAAOC,QAAQZ,GAClCa,KAAI,EAAEC,EAASP,MAAa,CAAGO,QAASC,SAASD,GAAUP,cAC3DS,MAAK,CAACC,EAAGC,IAAMA,EAAEX,QAAUU,EAAEV,UAE1BY,EAAQ,CAAC,EACfT,EAAcpB,SAAQ,CAAC8B,EAAMC,KAC3BF,EAAMC,EAAKN,SAAWO,EAAQ,CAAC,IAIjC,MAAMC,EAAgBZ,EAAcP,QAAO,CAACC,EAAKgB,IAAShB,EAAMgB,EAAKb,SAAS,GAAKG,EAAca,OAEjG,MAAO,CACLvB,WACAC,iBACAkB,QACAG,gBACApB,oBAEJ,GAIWsB,GAAmB,CAC9B,aAAMC,CAAQ1B,EAAU5C,EAAU2D,EAAU,KAAMY,EAAU,CAAC,SACrDlF,EAAM,KAGZ,MAAMqC,QAAeN,GAAezB,SAAS6E,MAAK5C,GAAWA,EAAQtB,MAAKuB,GAAKA,EAAEjC,KAAOgD,MAClFlC,QAAeH,EAAeF,QAAQL,GAG5C,IAAIE,EAAS,GACb,GAAIyD,EAAS,CACX,MAAMvD,QAAcV,EAAcW,QAAQsD,GACtCvD,IAAOF,EAAS,CAACE,GACvB,MACEF,QAAeR,EAAcO,YAAYD,GAI3C,MAAM,SAAE6C,EAAQ,eAAEC,EAAc,MAAEkB,EAAK,cAAEG,EAAa,kBAAEpB,SAChDhB,GAAaY,kBAAkB3C,EAAU4C,GAG3C6B,EAAYvE,EAAOwD,KAAItD,IAAS,CACpCR,GAAI,GAAGgD,KAAY5C,KAAYI,EAAMR,KACrCgD,WACA5C,WACA2D,QAASvD,EAAMR,GACf8E,UAAW,GAAGtE,EAAMP,OAAOO,EAAMN,SACjCC,UAAWK,EAAML,UACjBW,OAAQA,EAAOb,IACf6B,OAAQA,EAAOL,KACf+B,QAASP,EAASzC,EAAMR,IACxB+E,aAAc7B,EAAe1C,EAAMR,IACnCgF,KAAMZ,EAAM5D,EAAMR,IAClBY,SAAUE,EAAOF,SACjB2D,gBACApB,oBACA8B,gBAAgB,IAAIxH,MAAOyH,cAC3BP,cAGF,OAAOE,CACT,EAEA,oBAAMM,CAAeC,GAGnB,aAFM3F,EAAM,KAEL,CAAE4F,IAAK,cAAcD,QAC9B,GAIWE,GAAiB,CAE5B,sBAAMC,CAAiBC,EAAU,CAAC,SAC1B/F,EAAM,KAEZ,MAAMgG,EAAgB,CACpB,CACEzF,GAAI,EACJ0F,KAAM,aACN3B,QAAS,EACT4B,QAAS,IACTC,MAAO,qBACPC,aAAc,QAEhB,CACE7F,GAAI,EACJ0F,KAAM,aACN3B,QAAS,EACT4B,QAAS,KACTC,MAAO,iBACPC,aAAc,QAEhB,CACE7F,GAAI,EACJ0F,KAAM,aACN3B,QAAS,EACT4B,QAAS,IACTC,MAAO,qBACPC,aAAc,QAEhB,CACE7F,GAAI,EACJ0F,KAAM,aACN3B,QAAS,EACT4B,QAAS,KACTC,MAAO,sBACPC,aAAc,SAKlB,IAAIC,EAAS,IAAIL,GAUjB,GARID,EAAQ9D,YACVoE,EAASA,EAAOvF,QAAO0B,GAAK,IAAIxE,KAAKwE,EAAEyD,OAAS,IAAIjI,KAAK+H,EAAQ9D,cAG/D8D,EAAQ7D,UACVmE,EAASA,EAAOvF,QAAO0B,GAAK,IAAIxE,KAAKwE,EAAEyD,OAAS,IAAIjI,KAAK+H,EAAQ7D,YAG/D6D,EAAQO,WAAY,CACtB,MAAMC,EAAOR,EAAQO,WAAWE,cAE1B3F,QAAeR,EAAcC,SAEnC+F,EAASA,EAAOvF,QAAO0B,IACrB,MAAMzB,EAAQF,EAAOI,MAAKwF,GAAMA,EAAGlG,KAAOiC,EAAE8B,UAC5C,OAAOvD,IACLA,EAAMP,IAAIgG,cAAcE,SAASH,IACjCxF,EAAMN,OAAO+F,cAAcE,SAASH,IACpCxF,EAAML,UAAU8F,cAAcE,SAASH,GACxC,GAEL,CAEA,OAAOF,CACT,EAEA,sBAAMM,CAAiBC,GAGrB,aAFM5G,EAAM,KAEL,IAAK4G,EAAcrG,GAAIvC,KAAK2D,MACrC,EAEA,yBAAMkF,CAAoBC,GAGxB,aAFM9G,EAAM,KAEL,CAAE4F,IAAK,UAAUkB,QAC1B,EAGA,sBAAMC,CAAiBhB,EAAU,CAAC,SAC1B/F,EAAM,KAEZ,MAAMgH,EAAgB,CACpB,CACEzG,GAAI,EACJ0F,KAAM,aACNgB,aAAc,wBACdf,QAAS,KACTC,MAAO,oBACPe,aAAc,mBAEhB,CACE3G,GAAI,EACJ0F,KAAM,aACNgB,aAAc,SACdf,QAAS,KACTC,MAAO,sBACPe,aAAc,uBAKlB,IAAIb,EAAS,IAAIW,GAUjB,GARIjB,EAAQ9D,YACVoE,EAASA,EAAOvF,QAAOqG,GAAK,IAAInJ,KAAKmJ,EAAElB,OAAS,IAAIjI,KAAK+H,EAAQ9D,cAG/D8D,EAAQ7D,UACVmE,EAASA,EAAOvF,QAAOqG,GAAK,IAAInJ,KAAKmJ,EAAElB,OAAS,IAAIjI,KAAK+H,EAAQ7D,YAG/D6D,EAAQkB,aAAc,CACxB,MAAMV,EAAOR,EAAQkB,aAAaT,cAClCH,EAASA,EAAOvF,QAAOqG,GAAKA,EAAEF,aAAaT,cAAcE,SAASH,IACpE,CAEA,OAAOF,CACT,EAEA,sBAAMe,CAAiBC,GAGrB,aAFMrH,EAAM,KAEL,IAAKqH,EAAc9G,GAAIvC,KAAK2D,MACrC,EAGA,iBAAM2F,CAAYC,SACVvH,EAAM,KAGZ,MAAMgG,QAAsBzH,KAAKuH,mBAC3BkB,QAAsBzI,KAAKwI,mBAE3BS,EAAqBxB,EAAcrC,QAAO,CAACC,EAAKpB,IAAMoB,EAAMpB,EAAE0D,SAAS,GACvEuB,EAAqBT,EAAcrD,QAAO,CAACC,EAAKuD,IAAMvD,EAAMuD,EAAEjB,SAAS,GAGvEwB,EAAmB,CACvBC,KAAM3B,EAAclF,QAAO0B,GAAwB,SAAnBA,EAAE4D,eAAyBzC,QAAO,CAACC,EAAKpB,IAAMoB,EAAMpB,EAAE0D,SAAS,GAC/F0B,KAAM5B,EAAclF,QAAO0B,GAAwB,SAAnBA,EAAE4D,eAAyBzC,QAAO,CAACC,EAAKpB,IAAMoB,EAAMpB,EAAE0D,SAAS,IAI3F2B,EAAS,IAAI,IAAIC,IAAI9B,EAAc3B,KAAI7B,GAAKA,EAAE2D,UAC9C4B,EAAoB,CAAC,EAO3B,OANAF,EAAO/E,SAAQqD,IACb4B,EAAkB5B,GAASH,EACxBlF,QAAO0B,GAAKA,EAAE2D,QAAUA,IACxBxC,QAAO,CAACC,EAAKpB,IAAMoB,EAAMpB,EAAE0D,SAAS,EAAE,IAGpC,CACLsB,qBACAC,qBACAO,MAAOR,EAAqBC,EAC5BC,mBACAK,oBACAR,UAEJ,GAIWU,GAAsB,CACjC,4BAAMC,SACElI,EAAM,KAGZ,MAAM4C,QAAiBtB,GAAgBhB,SAGjC6H,EAAqB,CAAC,EAS5B,OARAvF,EAASE,SAAQpB,IACfyG,EAAmBzG,EAAQnB,IAAM,CAC/BmB,QAASA,EAAQlB,IACjBuD,SAA0B,EAAhBf,KAAKE,SAAe,IAAIkF,QAAQ,GAC1CC,aAAcrF,KAAKC,MAAsB,GAAhBD,KAAKE,SAAgB,IAC/C,IAGIiF,CACT,EAEA,2BAAMG,CAAsB7K,GAI1B,aAHMuC,EAAM,KAGL,CACLuI,WAAY,CAAC,cAAe,cAAe,eAC3C/E,SAAU,CAAC,MAAO,MAAO,OACzB6E,aAAc,CAAC,GAAI,GAAI,IAE3B,EAEA,kBAAMG,CAAajF,EAAUkF,EAAQ,SAC7BzI,EAAM,KAGZ,MAAMa,QAAeR,EAAcC,SAG7BoI,EAAY7H,EACf8H,MAAM,EAAG3F,KAAK4F,IAAIH,EAAO5H,EAAOkE,SAChCV,KAAItD,IAAS,CACZR,GAAIQ,EAAMR,GACVC,IAAK,GAAGO,EAAMP,OAAOO,EAAMN,SAC3BsD,SAA0B,EAAhBf,KAAKE,SAAe,IAAIkF,QAAQ,GAC1C/G,OAAQN,EAAMJ,aAEf6D,MAAK,CAACC,EAAGC,IAAMA,EAAEX,QAAUU,EAAEV,UAEhC,OAAO2E,CACT,GCpeF/J,EAAAA,WAAIC,IAAIiK,EAAAA,IAER,WAAmBA,EAAAA,GAAAA,MAAW,CAC5BC,MAAO,CAELC,YAAa,CAAEvI,IAAK,iBAAkBwI,KAAM,SAC5CC,iBAAiB,EACjBC,WAAW,EAGXlD,cAAe,GACfgB,cAAe,GAGfpE,SAAU,GACVL,QAAS,GAGTM,MAAO,CAAC,EAGRuC,UAAW,GAGXvE,OAAQ,GACRO,QAAS,GACT+H,cAAe,aAGjBC,UAAW,CACTC,WAAAA,CAAYP,EAAOI,GACjBJ,EAAMI,UAAYA,CACpB,EAGAI,iBAAAA,CAAkBR,EAAO9C,GACvB8C,EAAM9C,cAAgBA,CACxB,EACAuD,gBAAAA,CAAiBT,EAAOlC,GACtBkC,EAAM9C,cAAcwD,QAAQ5C,EAC9B,EACA6C,iBAAAA,CAAkBX,EAAO9B,GACvB8B,EAAM9B,cAAgBA,CACxB,EACA0C,gBAAAA,CAAiBZ,EAAOzB,GACtByB,EAAM9B,cAAcwC,QAAQnC,EAC9B,EAGAsC,YAAAA,CAAab,EAAOlG,GAClBkG,EAAMlG,SAAWA,CACnB,EACAgH,WAAAA,CAAYd,EAAOpH,GACjBoH,EAAMlG,SAASiH,KAAKnI,EACtB,EACAoI,cAAAA,CAAehB,GAAO,GAAEvI,EAAE,QAAEmB,IAC1B,MAAMmD,EAAQiE,EAAMlG,SAASmH,WAAUC,GAAKA,EAAEzJ,KAAOA,KACtC,IAAXsE,GACFiE,EAAMlG,SAASqH,OAAOpF,EAAO,EAAGnD,EAEpC,EACAwI,cAAAA,CAAepB,EAAOvI,GACpBuI,EAAMlG,SAAWkG,EAAMlG,SAAS9B,QAAOkJ,GAAKA,EAAEzJ,KAAOA,GACvD,EACA4J,WAAAA,CAAYrB,EAAOvG,GACjBuG,EAAMvG,QAAUA,CAClB,EACA6H,UAAAA,CAAWtB,EAAOzG,GAChByG,EAAMvG,QAAQsH,KAAKxH,EACrB,EACAgI,aAAAA,CAAcvB,GAAO,GAAEvI,EAAE,OAAE8B,IACzB,MAAMwC,EAAQiE,EAAMvG,QAAQwH,WAAUvH,GAAKA,EAAEjC,KAAOA,KACrC,IAAXsE,GACFiE,EAAMvG,QAAQ0H,OAAOpF,EAAO,EAAGxC,EAEnC,EAGAiI,SAAAA,CAAUxB,GAAO,SAAEnI,EAAQ,SAAE4C,EAAQ,MAAEV,IACrClE,EAAAA,WAAI4L,IAAIzB,EAAMjG,MAAO,GAAGlC,KAAY4C,IAAYV,EAClD,EACA2H,WAAAA,CAAY1B,GAAO,SAAEnI,EAAQ,SAAE4C,EAAQ,QAAEe,EAAO,UAAEmG,EAAS,KAAEC,IAC3D,MAAMC,EAAM,GAAGhK,KAAY4C,IACvBuF,EAAMjG,MAAM8H,IAAQ7B,EAAMjG,MAAM8H,GAAKrG,IAAYwE,EAAMjG,MAAM8H,GAAKrG,GAASmG,KAC7E3B,EAAMjG,MAAM8H,GAAKrG,GAASmG,GAAa,IAAK3B,EAAMjG,MAAM8H,GAAKrG,GAASmG,MAAeC,GAEzF,EAGAE,aAAAA,CAAc9B,EAAO1D,GACnB0D,EAAM1D,UAAYA,CACpB,EACAyF,YAAAA,CAAa/B,EAAOgC,GAClBhC,EAAM1D,UAAUyE,KAAKiB,EACvB,EAGAC,UAAAA,CAAWjC,EAAOjI,GAChBiI,EAAMjI,OAASA,CACjB,EACAmK,WAAAA,CAAYlC,EAAO1H,GACjB0H,EAAM1H,QAAUA,CAClB,EACA6J,kBAAAA,CAAmBnC,EAAOK,GACxBL,EAAMK,cAAgBA,CACxB,GAGF+B,QAAS,CAEP,wBAAMC,EAAmB,OAAEC,GAAUrF,EAAU,CAAC,GAC9CqF,EAAO,eAAe,GACtB,IACE,MAAMpF,QAAsBH,GAAeC,iBAAiBC,GAC5DqF,EAAO,oBAAqBpF,EAC9B,CAAE,MAAOqF,GACPC,QAAQD,MAAM,oDAAqDA,EACrE,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAEA,sBAAMzE,EAAiB,OAAEyE,GAAUxE,GACjCwE,EAAO,eAAe,GACtB,IACE,MAAMG,QAA0B1F,GAAec,iBAAiBC,GAEhE,OADAwE,EAAO,mBAAoBG,GACpBA,CACT,CAAE,MAAOF,GAEP,MADAC,QAAQD,MAAM,qDAAwDA,GAChEA,CACR,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAGA,mBAAMI,EAAc,OAAEJ,IACpBA,EAAO,eAAe,GACtB,IACE,MAAMxI,QAAiBtB,GAAgBhB,SACvC8K,EAAO,eAAgBxI,EACzB,CAAE,MAAOyI,GACPC,QAAQD,MAAM,+CAAgDA,EAChE,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAEA,mBAAMK,EAAc,OAAEL,GAAU1J,GAC9B0J,EAAO,eAAe,GACtB,IACE,MAAMM,QAAqBpK,GAAgBG,OAAOC,GAElD,OADA0J,EAAO,cAAeM,GACfA,CACT,CAAE,MAAOL,GAEP,MADAC,QAAQD,MAAM,4CAA6CA,GACrDA,CACR,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAEA,mBAAMO,EAAc,OAAEP,IAAU,GAAE7K,EAAE,QAAEmB,IACpC0J,EAAO,eAAe,GACtB,IACE,MAAMQ,QAAuBtK,GAAgBM,OAAOrB,EAAImB,GAExD,OADA0J,EAAO,iBAAkB,CAAE7K,KAAImB,QAASkK,IACjCA,CACT,CAAE,MAAOP,GAEP,MADAC,QAAQD,MAAM,+CAAgDA,GACxDA,CACR,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAEA,mBAAMS,EAAc,OAAET,GAAU7K,GAC9B6K,EAAO,eAAe,GACtB,UACQ9J,GAAgBO,OAAOtB,GAC7B6K,EAAO,iBAAkB7K,EAC3B,CAAE,MAAO8K,GAEP,MADAC,QAAQD,MAAM,+CAAgDA,GACxDA,CACR,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAGA,kBAAMU,EAAa,OAAEV,IACnBA,EAAO,eAAe,GACtB,IACE,MAAM7I,QAAgBR,GAAezB,SACrC8K,EAAO,cAAe7I,EACxB,CAAE,MAAO8I,GACPC,QAAQD,MAAM,8CAA+CA,EAC/D,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAEA,kBAAMW,EAAa,OAAEX,GAAU/I,GAC7B+I,EAAO,eAAe,GACtB,IACE,MAAMY,QAAoBjK,GAAeN,OAAOY,GAEhD,OADA+I,EAAO,aAAcY,GACdA,CACT,CAAE,MAAOX,GAEP,MADAC,QAAQD,MAAM,0CAA4CA,GACpDA,CACR,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAEA,8BAAMa,EAAyB,OAAEb,GAAU7K,GACzC6K,EAAO,eAAe,GACtB,IACE,MAAMc,QAAsBnK,GAAeO,mBAAmB/B,GAE9D,OADA6K,EAAO,gBAAiB,CAAE7K,KAAI8B,OAAQ6J,IAC/BA,CACT,CAAE,MAAOb,GAEP,MADAC,QAAQD,MAAM,0DAA4DA,GACpEA,CACR,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAGA,gBAAMe,EAAW,OAAEf,IAAU,SAAEzK,EAAQ,SAAE4C,IACvC6H,EAAO,eAAe,GACtB,IACE,MAAMvI,QAAcH,GAAaC,qBAAqBhC,EAAU4C,GAEhE,OADA6H,EAAO,YAAa,CAAEzK,WAAU4C,WAAUV,UACnCA,CACT,CAAE,MAAOwI,GAEP,MADAC,QAAQD,MAAM,4CAA6CA,GACrDA,CACR,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAEA,eAAMhI,EAAU,OAAEgI,IAAU,SAAEzK,EAAQ,SAAE4C,EAAQ,MAAEV,IAChDuI,EAAO,eAAe,GACtB,IAGE,aAFM1I,GAAaU,UAAUzC,EAAU4C,EAAUV,GACjDuI,EAAO,YAAa,CAAEzK,WAAU4C,WAAUV,UACnC,CAAEf,SAAS,EACpB,CAAE,MAAOuJ,GAEP,MADAC,QAAQD,MAAM,6CAA+CA,GACvDA,CACR,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAEA,uBAAM9H,EAAkB,OAAE8H,IAAU,SAAEzK,EAAQ,SAAE4C,IAC9C6H,EAAO,eAAe,GACtB,IACE,MAAM/E,QAAe3D,GAAaY,kBAAkB3C,EAAU4C,GAC9D,OAAO8C,CACT,CAAE,MAAOgF,GAEP,MADAC,QAAQD,MAAM,sCAAuCA,GAC/CA,CACR,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAGA,uBAAMgB,EAAkB,OAAEhB,IAAU,SAAE7H,EAAQ,SAAE5C,EAAQ,QAAE2D,EAAO,QAAEY,IACjEkG,EAAO,eAAe,GACtB,IACE,MAAMhG,QAAkBJ,GAAiBC,QAAQ1B,EAAU5C,EAAU2D,EAASY,GAE9E,OADAkG,EAAO,gBAAiBhG,GACjBA,CACT,CAAE,MAAOiG,GAEP,MADAC,QAAQD,MAAM,8CAA+CA,GACvDA,CACR,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAGA,iBAAMiB,EAAY,OAAEjB,IAClBA,EAAO,eAAe,GACtB,IACE,MAAMvK,QAAeR,EAAcC,SACnC8K,EAAO,aAAcvK,EACvB,CAAE,MAAOwK,GACPC,QAAQD,MAAM,6CAA8CA,EAC9D,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAEA,kBAAMkB,EAAa,OAAElB,IACnBA,EAAO,eAAe,GACtB,IACE,MAAMhK,QAAgBF,EAAeZ,SACrC8K,EAAO,cAAehK,EACxB,CAAE,MAAOiK,GACPC,QAAQD,MAAM,8CAA+CA,EAC/D,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAGA,iCAAMmB,EAA4B,OAAEnB,GAAU7H,GAC5C6H,EAAO,eAAe,GACtB,IACE,aAAanD,GAAoBC,uBAAuB3E,EAC1D,CAAE,MAAO8H,GAEP,MADAC,QAAQD,MAAM,+DAAgEA,GACxEA,CACR,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,EAEA,6BAAMoB,EAAwB,OAAEpB,GAAUzK,GACxCyK,EAAO,eAAe,GACtB,IACE,aAAanD,GAAoBK,sBAAsB3H,EACzD,CAAE,MAAO0K,GAEP,MADAC,QAAQD,MAAM,+DAAiEA,GACzEA,CACR,CAAE,QACAD,EAAO,eAAe,EACxB,CACF,GAGFqB,QAAS,CAEPjF,mBAAoBsB,GACXA,EAAM9C,cAAcrC,QAAO,CAAC+I,EAAOlK,IAAMkK,EAAQlK,EAAE0D,SAAS,GAErEyG,sBAAuB7D,GAAS,CAAC8D,EAAOC,IAC/B/D,EAAM9C,cAAclF,QAAO0B,IAChC,MAAMyD,EAAO,IAAIjI,KAAKwE,EAAEyD,MACxB,OAAOA,GAAQ2G,GAAS3G,GAAQ4G,CAAG,IAKvCC,eAAgBhE,GACP,IAAIA,EAAMlG,UAAU4B,MAAK,CAACC,EAAGC,IAAMD,EAAEjE,IAAIuM,cAAcrI,EAAElE,OAIlEwM,aAAclE,GAAS,CAACnI,EAAU4C,EAAUe,KAC1C,MAAMqG,EAAM,GAAGhK,KAAY4C,IAC3B,OAAIuF,EAAMjG,MAAM8H,GACP7B,EAAMjG,MAAM8H,GAAKrG,GAEnB,IAAI,EAIb2I,eAAgBnE,GAASnI,GAChBmI,EAAMjI,OAAOC,QAAO0B,GAAKA,EAAE7B,WAAaA,IAEjDuM,aAAcpE,GAASvI,GACduI,EAAMjI,OAAOI,MAAKuB,GAAKA,EAAEjC,KAAOA,IAEzC4M,cAAerE,GAASvI,GACfuI,EAAM1H,QAAQH,MAAKmM,GAAKA,EAAE7M,KAAOA,IAI1C2I,UAAWJ,GAASA,EAAMI,aC7X9BvK,EAAAA,WAAI0O,UAAUC,KAAOC,EAErB5O,EAAAA,WAAI6O,OAAOC,eAAgB,EAE3B,IAAI9O,EAAAA,WAAI,CACNiB,OAAM,EACN8N,MAAK,GACLhP,OAAQiP,GAAKA,EAAEC,KACdC,OAAO,O,GChBNC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CAGjDG,QAAS,CAAC,GAOX,OAHAE,EAAoBL,GAAUM,KAAKF,EAAOD,QAASC,EAAQA,EAAOD,QAASJ,GAGpEK,EAAOD,OACf,CAGAJ,EAAoB/D,EAAIqE,E,WCzBxB,IAAIE,EAAW,GACfR,EAAoBS,EAAI,SAASnI,EAAQoI,EAAUC,EAAIC,GACtD,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASC,EAAI,EAAGA,EAAIP,EAASxJ,OAAQ+J,IAAK,CACrCL,EAAWF,EAASO,GAAG,GACvBJ,EAAKH,EAASO,GAAG,GACjBH,EAAWJ,EAASO,GAAG,GAE3B,IAJA,IAGIC,GAAY,EACPC,EAAI,EAAGA,EAAIP,EAAS1J,OAAQiK,MACpB,EAAXL,GAAsBC,GAAgBD,IAAaxK,OAAO8K,KAAKlB,EAAoBS,GAAGU,OAAM,SAASvE,GAAO,OAAOoD,EAAoBS,EAAE7D,GAAK8D,EAASO,GAAK,IAChKP,EAASxE,OAAO+E,IAAK,IAErBD,GAAY,EACTJ,EAAWC,IAAcA,EAAeD,IAG7C,GAAGI,EAAW,CACbR,EAAStE,OAAO6E,IAAK,GACrB,IAAIK,EAAIT,SACER,IAANiB,IAAiB9I,EAAS8I,EAC/B,CACD,CACA,OAAO9I,CArBP,CAJCsI,EAAWA,GAAY,EACvB,IAAI,IAAIG,EAAIP,EAASxJ,OAAQ+J,EAAI,GAAKP,EAASO,EAAI,GAAG,GAAKH,EAAUG,IAAKP,EAASO,GAAKP,EAASO,EAAI,GACrGP,EAASO,GAAK,CAACL,EAAUC,EAAIC,EAwB/B,C,eC5BAZ,EAAoBqB,EAAI,SAAShB,GAChC,IAAIiB,EAASjB,GAAUA,EAAOkB,WAC7B,WAAa,OAAOlB,EAAO,UAAY,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAL,EAAoB5G,EAAEkI,EAAQ,CAAE5K,EAAG4K,IAC5BA,CACR,C,eCNAtB,EAAoB5G,EAAI,SAASgH,EAASoB,GACzC,IAAI,IAAI5E,KAAO4E,EACXxB,EAAoByB,EAAED,EAAY5E,KAASoD,EAAoByB,EAAErB,EAASxD,IAC5ExG,OAAOsL,eAAetB,EAASxD,EAAK,CAAE+E,YAAY,EAAMC,IAAKJ,EAAW5E,IAG3E,C,eCPAoD,EAAoB6B,EAAI,CAAC,EAGzB7B,EAAoBvL,EAAI,SAASqN,GAChC,OAAO3P,QAAQ4P,IAAI3L,OAAO8K,KAAKlB,EAAoB6B,GAAGjM,QAAO,SAASoM,EAAUpF,GAE/E,OADAoD,EAAoB6B,EAAEjF,GAAKkF,EAASE,GAC7BA,CACR,GAAG,IACJ,C,eCPAhC,EAAoBiC,EAAI,SAASH,GAEhC,MAAO,MAAQA,EAAU,IAAM,CAAC,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,YAAYA,GAAW,KACxO,C,eCHA9B,EAAoBkC,SAAW,SAASJ,GAEvC,MAAO,OAASA,EAAU,IAAM,CAAC,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,YAAYA,GAAW,MACxN,C,eCJA9B,EAAoBmC,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO5R,MAAQ,IAAI6R,SAAS,cAAb,EAChB,CAAE,MAAO5N,GACR,GAAsB,kBAAX6N,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,eCAxBtC,EAAoByB,EAAI,SAASc,EAAKC,GAAQ,OAAOpM,OAAOkJ,UAAUmD,eAAelC,KAAKgC,EAAKC,EAAO,C,eCAtG,IAAIE,EAAa,CAAC,EACdC,EAAoB,2BAExB3C,EAAoB4C,EAAI,SAAS/K,EAAKgL,EAAMjG,EAAKkF,GAChD,GAAGY,EAAW7K,GAAQ6K,EAAW7K,GAAKiE,KAAK+G,OAA3C,CACA,IAAIC,EAAQC,EACZ,QAAW5C,IAARvD,EAEF,IADA,IAAIoG,EAAUC,SAASC,qBAAqB,UACpCnC,EAAI,EAAGA,EAAIiC,EAAQhM,OAAQ+J,IAAK,CACvC,IAAIoC,EAAIH,EAAQjC,GAChB,GAAGoC,EAAEC,aAAa,QAAUvL,GAAOsL,EAAEC,aAAa,iBAAmBT,EAAoB/F,EAAK,CAAEkG,EAASK,EAAG,KAAO,CACpH,CAEGL,IACHC,GAAa,EACbD,EAASG,SAASI,cAAc,UAEhCP,EAAOQ,QAAU,QACjBR,EAAOS,QAAU,IACbvD,EAAoBwD,IACvBV,EAAOW,aAAa,QAASzD,EAAoBwD,IAElDV,EAAOW,aAAa,eAAgBd,EAAoB/F,GAExDkG,EAAOY,IAAM7L,GAEd6K,EAAW7K,GAAO,CAACgL,GACnB,IAAIc,EAAmB,SAASC,EAAMC,GAErCf,EAAOgB,QAAUhB,EAAOiB,OAAS,KACjCC,aAAaT,GACb,IAAIU,EAAUvB,EAAW7K,GAIzB,UAHO6K,EAAW7K,GAClBiL,EAAOoB,YAAcpB,EAAOoB,WAAWC,YAAYrB,GACnDmB,GAAWA,EAAQlP,SAAQ,SAAS4L,GAAM,OAAOA,EAAGkD,EAAQ,IACzDD,EAAM,OAAOA,EAAKC,EACtB,EACIN,EAAUlR,WAAWsR,EAAiBS,KAAK,UAAMjE,EAAW,CAAElM,KAAM,UAAWoQ,OAAQvB,IAAW,MACtGA,EAAOgB,QAAUH,EAAiBS,KAAK,KAAMtB,EAAOgB,SACpDhB,EAAOiB,OAASJ,EAAiBS,KAAK,KAAMtB,EAAOiB,QACnDhB,GAAcE,SAASqB,KAAKC,YAAYzB,EApCkB,CAqC3D,C,eCxCA9C,EAAoBoB,EAAI,SAAShB,GACX,qBAAXoE,QAA0BA,OAAOC,aAC1CrO,OAAOsL,eAAetB,EAASoE,OAAOC,YAAa,CAAEC,MAAO,WAE7DtO,OAAOsL,eAAetB,EAAS,aAAc,CAAEsE,OAAO,GACvD,C,eCNA1E,EAAoB2E,EAAI,G,eCAxB,GAAwB,qBAAb1B,SAAX,CACA,IAAI2B,EAAmB,SAAS9C,EAAS+C,EAAUC,EAAQ1S,EAAS2S,GACnE,IAAIC,EAAU/B,SAASI,cAAc,QAErC2B,EAAQC,IAAM,aACdD,EAAQ/Q,KAAO,WACX+L,EAAoBwD,KACvBwB,EAAQE,MAAQlF,EAAoBwD,IAErC,IAAI2B,EAAiB,SAAStB,GAG7B,GADAmB,EAAQlB,QAAUkB,EAAQjB,OAAS,KAChB,SAAfF,EAAM5P,KACT7B,QACM,CACN,IAAIgT,EAAYvB,GAASA,EAAM5P,KAC3BoR,EAAWxB,GAASA,EAAMQ,QAAUR,EAAMQ,OAAOiB,MAAQT,EACzDU,EAAM,IAAI7Q,MAAM,qBAAuBoN,EAAU,cAAgBsD,EAAY,KAAOC,EAAW,KACnGE,EAAItW,KAAO,iBACXsW,EAAIC,KAAO,wBACXD,EAAItR,KAAOmR,EACXG,EAAIE,QAAUJ,EACVL,EAAQd,YAAYc,EAAQd,WAAWC,YAAYa,GACvDD,EAAOQ,EACR,CACD,EAUA,OATAP,EAAQlB,QAAUkB,EAAQjB,OAASoB,EACnCH,EAAQM,KAAOT,EAGXC,EACHA,EAAOZ,WAAWwB,aAAaV,EAASF,EAAOa,aAE/C1C,SAASqB,KAAKC,YAAYS,GAEpBA,CACR,EACIY,EAAiB,SAASN,EAAMT,GAEnC,IADA,IAAIgB,EAAmB5C,SAASC,qBAAqB,QAC7CnC,EAAI,EAAGA,EAAI8E,EAAiB7O,OAAQ+J,IAAK,CAChD,IAAI+E,EAAMD,EAAiB9E,GACvBgF,EAAWD,EAAI1C,aAAa,cAAgB0C,EAAI1C,aAAa,QACjE,GAAe,eAAZ0C,EAAIb,MAAyBc,IAAaT,GAAQS,IAAalB,GAAW,OAAOiB,CACrF,CACA,IAAIE,EAAoB/C,SAASC,qBAAqB,SACtD,IAAQnC,EAAI,EAAGA,EAAIiF,EAAkBhP,OAAQ+J,IAAK,CAC7C+E,EAAME,EAAkBjF,GACxBgF,EAAWD,EAAI1C,aAAa,aAChC,GAAG2C,IAAaT,GAAQS,IAAalB,EAAU,OAAOiB,CACvD,CACD,EACIG,EAAiB,SAASnE,GAC7B,OAAO,IAAI3P,SAAQ,SAASC,EAAS2S,GACpC,IAAIO,EAAOtF,EAAoBkC,SAASJ,GACpC+C,EAAW7E,EAAoB2E,EAAIW,EACvC,GAAGM,EAAeN,EAAMT,GAAW,OAAOzS,IAC1CwS,EAAiB9C,EAAS+C,EAAU,KAAMzS,EAAS2S,EACpD,GACD,EAEImB,EAAqB,CACxB,IAAK,GAGNlG,EAAoB6B,EAAEsE,QAAU,SAASrE,EAASE,GACjD,IAAIoE,EAAY,CAAC,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,GAC5FF,EAAmBpE,GAAUE,EAASlG,KAAKoK,EAAmBpE,IACzB,IAAhCoE,EAAmBpE,IAAkBsE,EAAUtE,IACtDE,EAASlG,KAAKoK,EAAmBpE,GAAWmE,EAAenE,GAAS1K,MAAK,WACxE8O,EAAmBpE,GAAW,CAC/B,IAAG,SAASrN,GAEX,aADOyR,EAAmBpE,GACpBrN,CACP,IAEF,CA3E2C,C,eCK3C,IAAI4R,EAAkB,CACrB,IAAK,GAGNrG,EAAoB6B,EAAEZ,EAAI,SAASa,EAASE,GAE1C,IAAIsE,EAAqBtG,EAAoByB,EAAE4E,EAAiBvE,GAAWuE,EAAgBvE,QAAW3B,EACtG,GAA0B,IAAvBmG,EAGF,GAAGA,EACFtE,EAASlG,KAAKwK,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAIpU,SAAQ,SAASC,EAAS2S,GAAUuB,EAAqBD,EAAgBvE,GAAW,CAAC1P,EAAS2S,EAAS,IACzH/C,EAASlG,KAAKwK,EAAmB,GAAKC,GAGtC,IAAI1O,EAAMmI,EAAoB2E,EAAI3E,EAAoBiC,EAAEH,GAEpDxE,EAAQ,IAAI5I,MACZ8R,EAAe,SAAS3C,GAC3B,GAAG7D,EAAoByB,EAAE4E,EAAiBvE,KACzCwE,EAAqBD,EAAgBvE,GACX,IAAvBwE,IAA0BD,EAAgBvE,QAAW3B,GACrDmG,GAAoB,CACtB,IAAIlB,EAAYvB,IAAyB,SAAfA,EAAM5P,KAAkB,UAAY4P,EAAM5P,MAChEwS,EAAU5C,GAASA,EAAMQ,QAAUR,EAAMQ,OAAOX,IACpDpG,EAAMhI,QAAU,iBAAmBwM,EAAU,cAAgBsD,EAAY,KAAOqB,EAAU,IAC1FnJ,EAAMrO,KAAO,iBACbqO,EAAMrJ,KAAOmR,EACb9H,EAAMmI,QAAUgB,EAChBH,EAAmB,GAAGhJ,EACvB,CAEF,EACA0C,EAAoB4C,EAAE/K,EAAK2O,EAAc,SAAW1E,EAASA,EAE/D,CAEH,EAUA9B,EAAoBS,EAAEQ,EAAI,SAASa,GAAW,OAAoC,IAA7BuE,EAAgBvE,EAAgB,EAGrF,IAAI4E,EAAuB,SAASC,EAA4BC,GAC/D,IAKI3G,EAAU6B,EALVpB,EAAWkG,EAAK,GAChBC,EAAcD,EAAK,GACnBE,EAAUF,EAAK,GAGI7F,EAAI,EAC3B,GAAGL,EAASqG,MAAK,SAASvU,GAAM,OAA+B,IAAxB6T,EAAgB7T,EAAW,IAAI,CACrE,IAAIyN,KAAY4G,EACZ7G,EAAoByB,EAAEoF,EAAa5G,KACrCD,EAAoB/D,EAAEgE,GAAY4G,EAAY5G,IAGhD,GAAG6G,EAAS,IAAIxO,EAASwO,EAAQ9G,EAClC,CAEA,IADG2G,GAA4BA,EAA2BC,GACrD7F,EAAIL,EAAS1J,OAAQ+J,IACzBe,EAAUpB,EAASK,GAChBf,EAAoByB,EAAE4E,EAAiBvE,IAAYuE,EAAgBvE,IACrEuE,EAAgBvE,GAAS,KAE1BuE,EAAgBvE,GAAW,EAE5B,OAAO9B,EAAoBS,EAAEnI,EAC9B,EAEI0O,EAAqBC,KAAK,uCAAyCA,KAAK,wCAA0C,GACtHD,EAAmBjS,QAAQ2R,EAAqBtC,KAAK,KAAM,IAC3D4C,EAAmBlL,KAAO4K,EAAqBtC,KAAK,KAAM4C,EAAmBlL,KAAKsI,KAAK4C,G,ICpFvF,IAAIE,EAAsBlH,EAAoBS,OAAEN,EAAW,CAAC,MAAM,WAAa,OAAOH,EAAoB,KAAO,IACjHkH,EAAsBlH,EAAoBS,EAAEyG,E", "sources": ["webpack://projet_gestion_scolaire/./src/App.vue", "webpack://projet_gestion_scolaire/./src/components/layout/Header.vue", "webpack://projet_gestion_scolaire/./src/components/layout/Header.vue?9cc9", "webpack://projet_gestion_scolaire/./src/components/layout/Sidebar.vue", "webpack://projet_gestion_scolaire/./src/components/layout/Sidebar.vue?485e", "webpack://projet_gestion_scolaire/./src/components/layout/Footer.vue", "webpack://projet_gestion_scolaire/./src/components/layout/Footer.vue?3a0c", "webpack://projet_gestion_scolaire/./src/App.vue?7ccd", "webpack://projet_gestion_scolaire/./src/router/index.js", "webpack://projet_gestion_scolaire/./src/services/api.js", "webpack://projet_gestion_scolaire/./src/store/index.js", "webpack://projet_gestion_scolaire/./src/main.js", "webpack://projet_gestion_scolaire/webpack/bootstrap", "webpack://projet_gestion_scolaire/webpack/runtime/chunk loaded", "webpack://projet_gestion_scolaire/webpack/runtime/compat get default export", "webpack://projet_gestion_scolaire/webpack/runtime/define property getters", "webpack://projet_gestion_scolaire/webpack/runtime/ensure chunk", "webpack://projet_gestion_scolaire/webpack/runtime/get javascript chunk filename", "webpack://projet_gestion_scolaire/webpack/runtime/get mini-css chunk filename", "webpack://projet_gestion_scolaire/webpack/runtime/global", "webpack://projet_gestion_scolaire/webpack/runtime/hasOwnProperty shorthand", "webpack://projet_gestion_scolaire/webpack/runtime/load script", "webpack://projet_gestion_scolaire/webpack/runtime/make namespace object", "webpack://projet_gestion_scolaire/webpack/runtime/publicPath", "webpack://projet_gestion_scolaire/webpack/runtime/css loading", "webpack://projet_gestion_scolaire/webpack/runtime/jsonp chunk loading", "webpack://projet_gestion_scolaire/webpack/startup"], "sourcesContent": ["<template>\n  <div class=\"app\">\n    <Header />\n    <div class=\"main-container\">\n      <Sidebar />\n      <main class=\"content\">\n        <router-view />\n      </main>\n    </div>\n    <Footer />\n  </div>\n</template>\n\n<script>\nimport Header from './components/layout/Header.vue'\nimport Sidebar from './components/layout/Sidebar.vue'\nimport Footer from './components/layout/Footer.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    Header,\n    Sidebar,\n    Footer\n  },\n  created() {\n    // Charger les données partagées au démarrage de l'application\n    this.$store.dispatch('fetchEleves');\n    this.$store.dispatch('fetchClasses');\n    this.$store.dispatch('fetchMatieres');\n    this.$store.dispatch('fetchExamens');\n  }\n}\n</script>\n\n<style>\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\nbody {\n  font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  font-size: 16px;\n  line-height: 1.5;\n  color: #333;\n  background-color: #f9f9f9;\n}\n\n.app {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n}\n\n.main-container {\n  display: flex;\n  flex: 1;\n}\n\n.content {\n  flex: 1;\n  padding: 1rem;\n  overflow-y: auto;\n  background-color: #f9f9f9;\n}\n\n/* Utilitaires */\n.text-center {\n  text-align: center;\n}\n\n.mt-1 {\n  margin-top: 0.25rem;\n}\n\n.mt-2 {\n  margin-top: 0.5rem;\n}\n\n.mt-3 {\n  margin-top: 1rem;\n}\n\n.mt-4 {\n  margin-top: 1.5rem;\n}\n\n.mb-1 {\n  margin-bottom: 0.25rem;\n}\n\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\n\n.mb-3 {\n  margin-bottom: 1rem;\n}\n\n.mb-4 {\n  margin-bottom: 1.5rem;\n}\n</style>\n", "<template>\n  <header class=\"app-header\">\n    <div class=\"logo-container\">\n      <img src=\"@/assets/logo.png\" alt=\"Logo\" class=\"logo\" v-if=\"false\">\n      <h1>Collège Privé Adventiste Avaratetezana</h1>\n    </div>\n    <div class=\"user-menu\">\n      <span class=\"user-name\">Administrateur</span>\n      <button class=\"logout-btn\">Déconnexion</button>\n    </div>\n  </header>\n</template>\n\n<script>\nexport default {\n  name: 'AppHeader'\n}\n</script>\n\n<style scoped>\n.app-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  background-color: #3f51b5;\n  color: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.logo-container {\n  display: flex;\n  align-items: center;\n}\n\n.logo {\n  height: 40px;\n  margin-right: 1rem;\n}\n\nh1 {\n  font-size: 1.2rem;\n  margin: 0;\n}\n\n.user-menu {\n  display: flex;\n  align-items: center;\n}\n\n.user-name {\n  margin-right: 1rem;\n}\n\n.logout-btn {\n  background-color: transparent;\n  border: 1px solid white;\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.logout-btn:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n</style>\n", "import { render } from \"./Header.vue?vue&type=template&id=3e552c90&scoped=true\"\nimport script from \"./Header.vue?vue&type=script&lang=js\"\nexport * from \"./Header.vue?vue&type=script&lang=js\"\n\nimport \"./Header.vue?vue&type=style&index=0&id=3e552c90&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-3e552c90\"]])\n\nexport default __exports__", "<template>\n  <div class=\"sidebar\">\n    <div class=\"sidebar-header\">\n      <h3><PERSON>u Principal</h3>\n    </div>\n    <nav class=\"sidebar-nav\">\n      <ul>\n        <li>\n          <router-link to=\"/\" exact>\n            <i class=\"fas fa-home\"></i> Tableau de bord\n          </router-link>\n        </li>\n        <li class=\"menu-section\">\n          <span class=\"section-title\">Finance</span>\n          <ul>\n            <li>\n              <router-link to=\"/finance/encaissements\">\n                <i class=\"fas fa-money-bill-wave\"></i> Encaissements\n              </router-link>\n            </li>\n            <li>\n              <router-link to=\"/finance/decaissements\">\n                <i class=\"fas fa-file-invoice\"></i> Décaissements\n              </router-link>\n            </li>\n            <li>\n              <router-link to=\"/finance/synthese\">\n                <i class=\"fas fa-chart-pie\"></i> Synthèse\n              </router-link>\n            </li>\n          </ul>\n        </li>\n        <li class=\"menu-section\">\n          <span class=\"section-title\">Académique</span>\n          <ul>\n            <li>\n              <router-link to=\"/matieres\">\n                <i class=\"fas fa-book\"></i> Matières\n              </router-link>\n            </li>\n            <li>\n              <router-link to=\"/examens\">\n                <i class=\"fas fa-tasks\"></i> Examens\n              </router-link>\n            </li>\n            <li>\n              <router-link to=\"/notes\">\n                <i class=\"fas fa-pen\"></i> Saisie des notes\n              </router-link>\n            </li>\n            <li>\n              <router-link to=\"/bulletins\">\n                <i class=\"fas fa-file-alt\"></i> Bulletins\n              </router-link>\n            </li>\n          </ul>\n        </li>\n        <li class=\"menu-section\">\n          <span class=\"section-title\">Analyses</span>\n          <ul>\n            <li>\n              <router-link to=\"/archives\">\n                <i class=\"fas fa-archive\"></i> Archives\n              </router-link>\n            </li>\n            <li>\n              <router-link to=\"/statistiques\">\n                <i class=\"fas fa-chart-line\"></i> Statistiques\n              </router-link>\n            </li>\n          </ul>\n        </li>\n      </ul>\n    </nav>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'AppSidebar'\n}\n</script>\n\n<style scoped>\n.sidebar {\n  width: 250px;\n  height: 100%;\n  background-color: #f5f5f5;\n  border-right: 1px solid #e0e0e0;\n  overflow-y: auto;\n}\n\n.sidebar-header {\n  padding: 1rem;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.sidebar-header h3 {\n  margin: 0;\n  font-size: 1.2rem;\n  color: #333;\n}\n\n.sidebar-nav {\n  padding: 1rem 0;\n}\n\n.sidebar-nav ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.menu-section {\n  margin-bottom: 1rem;\n}\n\n.section-title {\n  display: block;\n  padding: 0.5rem 1rem;\n  font-weight: bold;\n  color: #757575;\n  font-size: 0.9rem;\n  text-transform: uppercase;\n}\n\n.sidebar-nav a {\n  display: block;\n  padding: 0.5rem 1rem 0.5rem 2rem;\n  color: #333;\n  text-decoration: none;\n  transition: background-color 0.3s;\n}\n\n.sidebar-nav a:hover {\n  background-color: #e0e0e0;\n}\n\n.sidebar-nav a.router-link-active {\n  background-color: #3f51b5;\n  color: white;\n}\n\n.sidebar-nav i {\n  margin-right: 0.5rem;\n  width: 20px;\n  text-align: center;\n}\n</style>\n", "import { render } from \"./Sidebar.vue?vue&type=template&id=7ed111e6&scoped=true\"\nimport script from \"./Sidebar.vue?vue&type=script&lang=js\"\nexport * from \"./Sidebar.vue?vue&type=script&lang=js\"\n\nimport \"./Sidebar.vue?vue&type=style&index=0&id=7ed111e6&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-7ed111e6\"]])\n\nexport default __exports__", "<template>\n  <footer class=\"app-footer\">\n    <div class=\"footer-content\">\n      <p>&copy; {{ currentYear }} Collège Privé Adventiste Avaratetezana - Tous droits réservés</p>\n      <p class=\"version\">Version 1.0.0</p>\n    </div>\n  </footer>\n</template>\n\n<script>\nexport default {\n  name: 'AppFooter',\n  computed: {\n    currentYear() {\n      return new Date().getFullYear();\n    }\n  }\n}\n</script>\n\n<style scoped>\n.app-footer {\n  background-color: #f5f5f5;\n  border-top: 1px solid #e0e0e0;\n  padding: 1rem;\n  text-align: center;\n  font-size: 0.9rem;\n  color: #757575;\n}\n\n.footer-content {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.version {\n  margin-top: 0.5rem;\n  font-size: 0.8rem;\n}\n</style>\n", "import { render } from \"./Footer.vue?vue&type=template&id=1024e7d1&scoped=true\"\nimport script from \"./Footer.vue?vue&type=script&lang=js\"\nexport * from \"./Footer.vue?vue&type=script&lang=js\"\n\nimport \"./Footer.vue?vue&type=style&index=0&id=1024e7d1&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-1024e7d1\"]])\n\nexport default __exports__", "import { render } from \"./App.vue?vue&type=template&id=5c9ea59c\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\n\nimport \"./App.vue?vue&type=style&index=0&id=5c9ea59c&lang=css\"\n\nimport exportComponent from \"../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "import Vue from 'vue'\nimport VueRouter from 'vue-router'\n\nVue.use(VueRouter)\n\n// Importation des vues\nconst Dashboard = () => import('../views/Dashboard.vue')\nconst FinanceEncaissement = () => import('../views/finance/Encaissement.vue')\nconst FinanceDecaissement = () => import('../views/finance/Decaissement.vue')\nconst FinanceSynthese = () => import('../views/finance/Synthese.vue')\nconst MatieresList = () => import('../views/matieres/MatieresList.vue')\nconst ExamensList = () => import('../views/examens/ExamensList.vue')\nconst NotesGestion = () => import('../views/notes/NotesGestion.vue')\nconst BulletinsGeneration = () => import('../views/bulletins/BulletinsGeneration.vue')\nconst ArchivesBulletins = () => import('../views/archives/ArchivesBulletins.vue')\nconst StatistiquesResultats = () => import('../views/statistiques/StatistiquesResultats.vue')\n\nconst routes = [\n  {\n    path: '/',\n    name: 'Dashboard',\n    component: Dashboard\n  },\n  // Module Finance\n  {\n    path: '/finance/encaissements',\n    name: 'FinanceEncaissement',\n    component: FinanceEncaissement\n  },\n  {\n    path: '/finance/decaissements',\n    name: 'FinanceDecaissement',\n    component: FinanceDecaissement\n  },\n  {\n    path: '/finance/synthese',\n    name: 'FinanceSynthese',\n    component: FinanceSynthese\n  },\n  // Module Matières et Examens\n  {\n    path: '/matieres',\n    name: 'MatieresList',\n    component: MatieresList\n  },\n  {\n    path: '/examens',\n    name: 'ExamensList',\n    component: ExamensList\n  },\n  // Module Notes\n  {\n    path: '/notes',\n    name: 'NotesGestion',\n    component: NotesGestion\n  },\n  // Module Bulletins\n  {\n    path: '/bulletins',\n    name: 'BulletinsGeneration',\n    component: BulletinsGeneration\n  },\n  // Module Archives et Statistiques\n  {\n    path: '/archives',\n    name: 'ArchivesBulletins',\n    component: ArchivesBulletins\n  },\n  {\n    path: '/statistiques',\n    name: 'StatistiquesResultats',\n    component: StatistiquesResultats\n  },\n  // Route par défaut en cas d'URL non trouvée\n  {\n    path: '*',\n    redirect: '/'\n  }\n]\n\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n})\n\nexport default router\n", "/* eslint-disable no-unused-vars */\n// Simuler une API backend avec des délais pour imiter les appels réseau\nconst delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));\n\n// Service pour la gestion des élèves\nexport const ElevesService = {\n  async getAll() {\n    await delay(300);\n    return [\n      { id: 1, nom: '<PERSON><PERSON>', prenom: '<PERSON>', matricule: 'E001', classeId: 1 },\n      { id: 2, nom: '<PERSON>', prenom: '<PERSON>', matricule: 'E002', classeId: 1 },\n      { id: 3, nom: '<PERSON>ois', prenom: '<PERSON>', matricule: 'E003', classeId: 1 },\n      { id: 4, nom: 'Le<PERSON>bvre', prenom: '<PERSON>', matricule: 'E004', classeId: 2 },\n      { id: 5, nom: 'Moreau', prenom: '<PERSON>', matricule: 'E005', classeId: 2 },\n      { id: 6, nom: 'Petit', prenom: '<PERSON>', matricule: 'E006', classeId: 3 },\n      { id: 7, nom: '<PERSON>', prenom: '<PERSON>', matricule: 'E007', classeId: 3 }\n    ];\n  },\n  \n  async getByClasse(classeId) {\n    const eleves = await this.getAll();\n    return eleves.filter(eleve => eleve.classeId === classeId);\n  },\n  \n  async getById(id) {\n    const eleves = await this.getAll();\n    return eleves.find(eleve => eleve.id === id);\n  }\n};\n\n// Service pour la gestion des classes\nexport const ClassesService = {\n  async getAll() {\n    await delay(300);\n    return [\n      { id: 1, nom: '6ème A', effectif: 35 },\n      { id: 2, nom: '6ème B', effectif: 32 },\n      { id: 3, nom: '5ème A', effectif: 30 },\n      { id: 4, nom: '5ème B', effectif: 28 }\n    ];\n  },\n  \n  async getById(id) {\n    const classes = await this.getAll();\n    return classes.find(classe => classe.id === id);\n  }\n};\n\n// Service pour la gestion des matières\nexport const MatieresService = {\n  async getAll() {\n    await delay(300);\n    return [\n      { id: 1, nom: 'Mathématiques', abreviation: 'MATH', coefficient: 4 },\n      { id: 2, nom: 'Physique', abreviation: 'PHY', coefficient: 3 },\n      { id: 3, nom: 'Français', abreviation: 'FR', coefficient: 3 },\n      { id: 4, nom: 'Histoire-Géographie', abreviation: 'HIST', coefficient: 2 },\n      { id: 5, nom: 'Anglais', abreviation: 'ANG', coefficient: 2 },\n      { id: 6, nom: 'SVT', abreviation: 'SVT', coefficient: 2 },\n      { id: 7, nom: 'Éducation Physique', abreviation: 'EPS', coefficient: 1 },\n      { id: 8, nom: 'Arts Plastiques', abreviation: 'ART', coefficient: 1 }\n    ];\n  },\n  \n  async create(matiere) {\n    await delay(500);\n    // Simuler la création avec un nouvel ID\n    return { ...matiere, id: Date.now() };\n  },\n  \n  async update(_, matiere) {\n    await delay(500);\n    // Simuler la mise à jour\n    return { ...matiere };\n  },\n  \n  async delete(id) {\n    await delay(500);\n    // Simuler la suppression\n    return { success: true };\n  }\n};\n\n// Service pour la gestion des examens\nexport const ExamensService = {\n  async getAll() {\n    await delay(300);\n    return [\n      { \n        id: 1, \n        type: 'Trimestre 1', \n        dateDebut: '2024-09-15', \n        dateFin: '2024-10-30', \n        classesIds: [1, 2], \n        verrouille: true \n      },\n      { \n        id: 2, \n        type: 'Trimestre 1', \n        dateDebut: '2024-09-15', \n        dateFin: '2024-10-30', \n        classesIds: [3, 4], \n        verrouille: false \n      },\n      { \n        id: 3, \n        type: 'Trimestre 2', \n        dateDebut: '2024-12-01', \n        dateFin: '2025-01-15', \n        classesIds: [1, 2, 3, 4], \n        verrouille: false \n      }\n    ];\n  },\n  \n  async create(examen) {\n    await delay(500);\n    // Simuler la création avec un nouvel ID\n    return { ...examen, id: Date.now() };\n  },\n  \n  async update(id, examen) {\n    await delay(500);\n    // Simuler la mise à jour\n    return { ...examen, id };\n  },\n  \n  async toggleVerrouillage(id) {\n    await delay(300);\n    const examens = await this.getAll();\n    const examen = examens.find(e => e.id === id);\n    if (examen) {\n      examen.verrouille = !examen.verrouille;\n      return examen;\n    }\n    throw new Error('Examen non trouvé');\n  }\n};\n\n// Service pour la gestion des notes\nexport const NotesService = {\n  async getByClasseAndExamen(classeId, _) {\n    await delay(500);\n    \n    // Récupérer les élèves de la classe\n    const eleves = await ElevesService.getByClasse(classeId);\n    \n    // Récupérer les matières\n    const matieres = await MatieresService.getAll();\n    \n    // Générer des notes aléatoires pour chaque élève et chaque matière\n    const notes = {};\n    \n    eleves.forEach(eleve => {\n      notes[eleve.id] = {};\n      matieres.forEach(matiere => {\n        notes[eleve.id][matiere.id] = {\n          ds1: Math.floor(Math.random() * 10) + 10, // Entre 10 et 19\n          ds2: Math.floor(Math.random() * 10) + 10,\n          examen: Math.floor(Math.random() * 10) + 10\n        };\n      });\n    });\n    \n    return notes;\n  },\n  \n  async saveNotes() {\n    await delay(700);\n    // Simuler la sauvegarde\n    return { success: true, message: 'Notes enregistrées avec succès' };\n  },\n  \n  async calculateMoyennes(classeId, examenId) {\n    await delay(500);\n    \n    // Récupérer les élèves de la classe\n    const eleves = await ElevesService.getByClasse(classeId);\n    \n    // Récupérer les matières avec leurs coefficients\n    const matieres = await MatieresService.getAll();\n    \n    // Récupérer les notes\n    const notes = await this.getByClasseAndExamen(classeId, examenId);\n    \n    // Calculer les moyennes pour chaque élève\n    const moyennes = {};\n    const totauxPonderes = {};\n    const totalCoefficients = matieres.reduce((sum, matiere) => sum + matiere.coefficient, 0);\n    \n    eleves.forEach(eleve => {\n      let totalPoints = 0;\n      \n      matieres.forEach(matiere => {\n        const eleveNotes = notes[eleve.id][matiere.id];\n        const moyenne = (parseFloat(eleveNotes.ds1) + parseFloat(eleveNotes.ds2) + parseFloat(eleveNotes.examen)) / 3;\n        const points = moyenne * matiere.coefficient;\n        \n        totalPoints += points;\n      });\n      \n      moyennes[eleve.id] = totalPoints / totalCoefficients;\n      totauxPonderes[eleve.id] = totalPoints;\n    });\n    \n    // Calculer les rangs\n    const moyennesArray = Object.entries(moyennes)\n      .map(([eleveId, moyenne]) => ({ eleveId: parseInt(eleveId), moyenne }))\n      .sort((a, b) => b.moyenne - a.moyenne);\n    \n    const rangs = {};\n    moyennesArray.forEach((item, index) => {\n      rangs[item.eleveId] = index + 1;\n    });\n    \n    // Calculer la moyenne de la classe\n    const moyenneClasse = moyennesArray.reduce((sum, item) => sum + item.moyenne, 0) / moyennesArray.length;\n    \n    return {\n      moyennes,\n      totauxPonderes,\n      rangs,\n      moyenneClasse,\n      totalCoefficients\n    };\n  }\n};\n\n// Service pour la gestion des bulletins\nexport const BulletinsService = {\n  async generer(examenId, classeId, eleveId = null, options = {}) {\n    await delay(1000);\n    \n    // Récupérer les informations nécessaires\n    const examen = await ExamensService.getAll().then(examens => examens.find(e => e.id === examenId));\n    const classe = await ClassesService.getById(classeId);\n    \n    // Récupérer les élèves concernés\n    let eleves = [];\n    if (eleveId) {\n      const eleve = await ElevesService.getById(eleveId);\n      if (eleve) eleves = [eleve];\n    } else {\n      eleves = await ElevesService.getByClasse(classeId);\n    }\n    \n    // Récupérer les calculs de moyennes\n    const { moyennes, totauxPonderes, rangs, moyenneClasse, totalCoefficients } = \n      await NotesService.calculateMoyennes(classeId, examenId);\n    \n    // Générer les bulletins\n    const bulletins = eleves.map(eleve => ({\n      id: `${examenId}_${classeId}_${eleve.id}`,\n      examenId,\n      classeId,\n      eleveId: eleve.id,\n      eleveName: `${eleve.nom} ${eleve.prenom}`,\n      matricule: eleve.matricule,\n      classe: classe.nom,\n      examen: examen.type,\n      moyenne: moyennes[eleve.id],\n      totalPondere: totauxPonderes[eleve.id],\n      rang: rangs[eleve.id],\n      effectif: classe.effectif,\n      moyenneClasse,\n      totalCoefficients,\n      dateGeneration: new Date().toISOString(),\n      options\n    }));\n    \n    return bulletins;\n  },\n  \n  async getBulletinPDF(bulletinId) {\n    await delay(700);\n    // Simuler la génération d'un PDF\n    return { url: `/bulletins/${bulletinId}.pdf` };\n  }\n};\n\n// Service pour la gestion financière\nexport const FinanceService = {\n  // Gestion des encaissements\n  async getEncaissements(filters = {}) {\n    await delay(400);\n    \n    const encaissements = [\n      { \n        id: 1, \n        date: '2024-03-15', \n        eleveId: 1, \n        montant: 50000, \n        motif: 'Frais de scolarité', \n        modePaiement: 'Cash' \n      },\n      { \n        id: 2, \n        date: '2024-03-20', \n        eleveId: 2, \n        montant: 25000, \n        motif: 'Frais d\\'examen', \n        modePaiement: 'ADRA' \n      },\n      { \n        id: 3, \n        date: '2024-03-22', \n        eleveId: 3, \n        montant: 50000, \n        motif: 'Frais de scolarité', \n        modePaiement: 'Cash' \n      },\n      { \n        id: 4, \n        date: '2024-03-25', \n        eleveId: 4, \n        montant: 15000, \n        motif: 'Frais d\\'inscription', \n        modePaiement: 'ADRA' \n      }\n    ];\n    \n    // Appliquer les filtres si nécessaire\n    let result = [...encaissements];\n    \n    if (filters.dateDebut) {\n      result = result.filter(e => new Date(e.date) >= new Date(filters.dateDebut));\n    }\n    \n    if (filters.dateFin) {\n      result = result.filter(e => new Date(e.date) <= new Date(filters.dateFin));\n    }\n    \n    if (filters.searchTerm) {\n      const term = filters.searchTerm.toLowerCase();\n      // Dans une vraie application, cette recherche serait faite côté serveur\n      const eleves = await ElevesService.getAll();\n      \n      result = result.filter(e => {\n        const eleve = eleves.find(el => el.id === e.eleveId);\n        return eleve && (\n          eleve.nom.toLowerCase().includes(term) || \n          eleve.prenom.toLowerCase().includes(term) || \n          eleve.matricule.toLowerCase().includes(term)\n        );\n      });\n    }\n    \n    return result;\n  },\n  \n  async saveEncaissement(encaissement) {\n    await delay(500);\n    // Simuler la sauvegarde\n    return { ...encaissement, id: Date.now() };\n  },\n  \n  async getEncaissementRecu(encaissementId) {\n    await delay(300);\n    // Simuler la génération d'un reçu\n    return { url: `/recus/${encaissementId}.pdf` };\n  },\n  \n  // Gestion des décaissements\n  async getDecaissements(filters = {}) {\n    await delay(400);\n    \n    const decaissements = [\n      { \n        id: 1, \n        date: '2024-03-10', \n        beneficiaire: 'Fournisseur Papeterie', \n        montant: 120000, \n        motif: 'Achat fournitures', \n        justificatif: 'facture_001.pdf' \n      },\n      { \n        id: 2, \n        date: '2024-03-18', \n        beneficiaire: 'JIRAMA', \n        montant: 350000, \n        motif: 'Facture électricité', \n        justificatif: 'facture_jirama.pdf' \n      }\n    ];\n    \n    // Appliquer les filtres si nécessaire\n    let result = [...decaissements];\n    \n    if (filters.dateDebut) {\n      result = result.filter(d => new Date(d.date) >= new Date(filters.dateDebut));\n    }\n    \n    if (filters.dateFin) {\n      result = result.filter(d => new Date(d.date) <= new Date(filters.dateFin));\n    }\n    \n    if (filters.beneficiaire) {\n      const term = filters.beneficiaire.toLowerCase();\n      result = result.filter(d => d.beneficiaire.toLowerCase().includes(term));\n    }\n    \n    return result;\n  },\n  \n  async saveDecaissement(decaissement) {\n    await delay(500);\n    // Simuler la sauvegarde\n    return { ...decaissement, id: Date.now() };\n  },\n  \n  // Synthèse financière\n  async getSynthese(periode) {\n    await delay(600);\n    \n    // Simuler des données de synthèse\n    const encaissements = await this.getEncaissements();\n    const decaissements = await this.getDecaissements();\n    \n    const totalEncaissements = encaissements.reduce((sum, e) => sum + e.montant, 0);\n    const totalDecaissements = decaissements.reduce((sum, d) => sum + d.montant, 0);\n    \n    // Répartition par mode de paiement\n    const repartitionModes = {\n      Cash: encaissements.filter(e => e.modePaiement === 'Cash').reduce((sum, e) => sum + e.montant, 0),\n      ADRA: encaissements.filter(e => e.modePaiement === 'ADRA').reduce((sum, e) => sum + e.montant, 0)\n    };\n    \n    // Répartition par motif\n    const motifs = [...new Set(encaissements.map(e => e.motif))];\n    const repartitionMotifs = {};\n    motifs.forEach(motif => {\n      repartitionMotifs[motif] = encaissements\n        .filter(e => e.motif === motif)\n        .reduce((sum, e) => sum + e.montant, 0);\n    });\n    \n    return {\n      totalEncaissements,\n      totalDecaissements,\n      solde: totalEncaissements - totalDecaissements,\n      repartitionModes,\n      repartitionMotifs,\n      periode\n    };\n  }\n};\n\n// Service pour les statistiques\nexport const StatistiquesService = {\n  async getResultatsParMatiere() {\n    await delay(700);\n    \n    // Récupérer les matières\n    const matieres = await MatieresService.getAll();\n    \n    // Simuler des moyennes par matière\n    const moyennesParMatiere = {};\n    matieres.forEach(matiere => {\n      moyennesParMatiere[matiere.id] = {\n        matiere: matiere.nom,\n        moyenne: (Math.random() * 6 + 10).toFixed(2), // Entre 10 et 16\n        tauxReussite: Math.floor(Math.random() * 40 + 60) // Entre 60% et 100%\n      };\n    });\n    \n    return moyennesParMatiere;\n  },\n  \n  async getEvolutionResultats(_) {\n    await delay(700);\n    \n    // Simuler l'évolution des résultats sur 3 trimestres\n    return {\n      trimestres: ['Trimestre 1', 'Trimestre 2', 'Trimestre 3'],\n      moyennes: [12.45, 13.22, 14.05],\n      tauxReussite: [68, 75, 82]\n    };\n  },\n  \n  async getTopEleves(examenId, limit = 5) {\n    await delay(500);\n    \n    // Récupérer les élèves\n    const eleves = await ElevesService.getAll();\n    \n    // Simuler le top des élèves\n    const topEleves = eleves\n      .slice(0, Math.min(limit, eleves.length))\n      .map(eleve => ({\n        id: eleve.id,\n        nom: `${eleve.nom} ${eleve.prenom}`,\n        moyenne: (Math.random() * 4 + 16).toFixed(2), // Entre 16 et 20\n        classe: eleve.classeId\n      }))\n      .sort((a, b) => b.moyenne - a.moyenne);\n    \n    return topEleves;\n  }\n};\n", "import Vue from 'vue'\nimport Vuex from 'vuex'\nimport { \n  ElevesService, \n  ClassesService, \n  MatieresService, \n  ExamensService, \n  NotesService, \n  BulletinsService, \n  FinanceService,\n  StatistiquesService \n} from '../services/api'\n\nVue.use(Vuex)\n\nexport default new Vuex.Store({\n  state: {\n    // État global de l'application\n    currentUser: { nom: 'Administrateur', role: 'admin' },\n    isAuthenticated: true,\n    isLoading: false,\n    \n    // Module Finance\n    encaissements: [],\n    decaissements: [],\n    \n    // Module Matières et Examens\n    matieres: [],\n    examens: [],\n    \n    // Module Notes\n    notes: {},\n    \n    // Module Bulletins\n    bulletins: [],\n    \n    // Données partagées\n    eleves: [],\n    classes: [],\n    anneeScolaire: '2024-2025'\n  },\n  \n  mutations: {\n    SET_LOADING(state, isLoading) {\n      state.isLoading = isLoading;\n    },\n    \n    // Mutations pour le module Finance\n    SET_ENCAISSEMENTS(state, encaissements) {\n      state.encaissements = encaissements;\n    },\n    ADD_ENCAISSEMENT(state, encaissement) {\n      state.encaissements.unshift(encaissement);\n    },\n    SET_DECAISSEMENTS(state, decaissements) {\n      state.decaissements = decaissements;\n    },\n    ADD_DECAISSEMENT(state, decaissement) {\n      state.decaissements.unshift(decaissement);\n    },\n    \n    // Mutations pour le module Matières et Examens\n    SET_MATIERES(state, matieres) {\n      state.matieres = matieres;\n    },\n    ADD_MATIERE(state, matiere) {\n      state.matieres.push(matiere);\n    },\n    UPDATE_MATIERE(state, { id, matiere }) {\n      const index = state.matieres.findIndex(m => m.id === id);\n      if (index !== -1) {\n        state.matieres.splice(index, 1, matiere);\n      }\n    },\n    DELETE_MATIERE(state, id) {\n      state.matieres = state.matieres.filter(m => m.id !== id);\n    },\n    SET_EXAMENS(state, examens) {\n      state.examens = examens;\n    },\n    ADD_EXAMEN(state, examen) {\n      state.examens.push(examen);\n    },\n    UPDATE_EXAMEN(state, { id, examen }) {\n      const index = state.examens.findIndex(e => e.id === id);\n      if (index !== -1) {\n        state.examens.splice(index, 1, examen);\n      }\n    },\n    \n    // Mutations pour le module Notes\n    SET_NOTES(state, { classeId, examenId, notes }) {\n      Vue.set(state.notes, `${classeId}_${examenId}`, notes);\n    },\n    UPDATE_NOTE(state, { classeId, examenId, eleveId, matiereId, note }) {\n      const key = `${classeId}_${examenId}`;\n      if (state.notes[key] && state.notes[key][eleveId] && state.notes[key][eleveId][matiereId]) {\n        state.notes[key][eleveId][matiereId] = { ...state.notes[key][eleveId][matiereId], ...note };\n      }\n    },\n    \n    // Mutations pour le module Bulletins\n    SET_BULLETINS(state, bulletins) {\n      state.bulletins = bulletins;\n    },\n    ADD_BULLETIN(state, bulletin) {\n      state.bulletins.push(bulletin);\n    },\n    \n    // Mutations pour les données partagées\n    SET_ELEVES(state, eleves) {\n      state.eleves = eleves;\n    },\n    SET_CLASSES(state, classes) {\n      state.classes = classes;\n    },\n    SET_ANNEE_SCOLAIRE(state, anneeScolaire) {\n      state.anneeScolaire = anneeScolaire;\n    }\n  },\n  \n  actions: {\n    // Actions pour le module Finance\n    async fetchEncaissements({ commit }, filters = {}) {\n      commit('SET_LOADING', true);\n      try {\n        const encaissements = await FinanceService.getEncaissements(filters);\n        commit('SET_ENCAISSEMENTS', encaissements);\n      } catch (error) {\n        console.error('Erreur lors de la récupération des encaissements:', error);\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    async saveEncaissement({ commit }, encaissement) {\n      commit('SET_LOADING', true);\n      try {\n        const savedEncaissement = await FinanceService.saveEncaissement(encaissement);\n        commit('ADD_ENCAISSEMENT', savedEncaissement);\n        return savedEncaissement;\n      } catch (error) {\n        console.error('Erreur lors de l\\'enregistrement de l\\'encaissement:', error);\n        throw error;\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    // Actions pour le module Matières\n    async fetchMatieres({ commit }) {\n      commit('SET_LOADING', true);\n      try {\n        const matieres = await MatieresService.getAll();\n        commit('SET_MATIERES', matieres);\n      } catch (error) {\n        console.error('Erreur lors de la récupération des matières:', error);\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    async createMatiere({ commit }, matiere) {\n      commit('SET_LOADING', true);\n      try {\n        const savedMatiere = await MatieresService.create(matiere);\n        commit('ADD_MATIERE', savedMatiere);\n        return savedMatiere;\n      } catch (error) {\n        console.error('Erreur lors de la création de la matière:', error);\n        throw error;\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    async updateMatiere({ commit }, { id, matiere }) {\n      commit('SET_LOADING', true);\n      try {\n        const updatedMatiere = await MatieresService.update(id, matiere);\n        commit('UPDATE_MATIERE', { id, matiere: updatedMatiere });\n        return updatedMatiere;\n      } catch (error) {\n        console.error('Erreur lors de la mise à jour de la matière:', error);\n        throw error;\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    async deleteMatiere({ commit }, id) {\n      commit('SET_LOADING', true);\n      try {\n        await MatieresService.delete(id);\n        commit('DELETE_MATIERE', id);\n      } catch (error) {\n        console.error('Erreur lors de la suppression de la matière:', error);\n        throw error;\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    // Actions pour le module Examens\n    async fetchExamens({ commit }) {\n      commit('SET_LOADING', true);\n      try {\n        const examens = await ExamensService.getAll();\n        commit('SET_EXAMENS', examens);\n      } catch (error) {\n        console.error('Erreur lors de la récupération des examens:', error);\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    async createExamen({ commit }, examen) {\n      commit('SET_LOADING', true);\n      try {\n        const savedExamen = await ExamensService.create(examen);\n        commit('ADD_EXAMEN', savedExamen);\n        return savedExamen;\n      } catch (error) {\n        console.error('Erreur lors de la création de l\\'examen:', error);\n        throw error;\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    async toggleExamenVerrouillage({ commit }, id) {\n      commit('SET_LOADING', true);\n      try {\n        const updatedExamen = await ExamensService.toggleVerrouillage(id);\n        commit('UPDATE_EXAMEN', { id, examen: updatedExamen });\n        return updatedExamen;\n      } catch (error) {\n        console.error('Erreur lors du verrouillage/déverrouillage de l\\'examen:', error);\n        throw error;\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    // Actions pour le module Notes\n    async fetchNotes({ commit }, { classeId, examenId }) {\n      commit('SET_LOADING', true);\n      try {\n        const notes = await NotesService.getByClasseAndExamen(classeId, examenId);\n        commit('SET_NOTES', { classeId, examenId, notes });\n        return notes;\n      } catch (error) {\n        console.error('Erreur lors de la récupération des notes:', error);\n        throw error;\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    async saveNotes({ commit }, { classeId, examenId, notes }) {\n      commit('SET_LOADING', true);\n      try {\n        await NotesService.saveNotes(classeId, examenId, notes);\n        commit('SET_NOTES', { classeId, examenId, notes });\n        return { success: true };\n      } catch (error) {\n        console.error('Erreur lors de l\\'enregistrement des notes:', error);\n        throw error;\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    async calculateMoyennes({ commit }, { classeId, examenId }) {\n      commit('SET_LOADING', true);\n      try {\n        const result = await NotesService.calculateMoyennes(classeId, examenId);\n        return result;\n      } catch (error) {\n        console.error('Erreur lors du calcul des moyennes:', error);\n        throw error;\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    // Actions pour le module Bulletins\n    async generateBulletins({ commit }, { examenId, classeId, eleveId, options }) {\n      commit('SET_LOADING', true);\n      try {\n        const bulletins = await BulletinsService.generer(examenId, classeId, eleveId, options);\n        commit('SET_BULLETINS', bulletins);\n        return bulletins;\n      } catch (error) {\n        console.error('Erreur lors de la génération des bulletins:', error);\n        throw error;\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    // Actions pour les données partagées\n    async fetchEleves({ commit }) {\n      commit('SET_LOADING', true);\n      try {\n        const eleves = await ElevesService.getAll();\n        commit('SET_ELEVES', eleves);\n      } catch (error) {\n        console.error('Erreur lors de la récupération des élèves:', error);\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    async fetchClasses({ commit }) {\n      commit('SET_LOADING', true);\n      try {\n        const classes = await ClassesService.getAll();\n        commit('SET_CLASSES', classes);\n      } catch (error) {\n        console.error('Erreur lors de la récupération des classes:', error);\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    // Actions pour les statistiques\n    async fetchStatistiquesParMatiere({ commit }, examenId) {\n      commit('SET_LOADING', true);\n      try {\n        return await StatistiquesService.getResultatsParMatiere(examenId);\n      } catch (error) {\n        console.error('Erreur lors de la récupération des statistiques par matière:', error);\n        throw error;\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    },\n    \n    async fetchEvolutionResultats({ commit }, classeId) {\n      commit('SET_LOADING', true);\n      try {\n        return await StatistiquesService.getEvolutionResultats(classeId);\n      } catch (error) {\n        console.error('Erreur lors de la récupération de l\\'évolution des résultats:', error);\n        throw error;\n      } finally {\n        commit('SET_LOADING', false);\n      }\n    }\n  },\n  \n  getters: {\n    // Getters pour le module Finance\n    totalEncaissements: state => {\n      return state.encaissements.reduce((total, e) => total + e.montant, 0);\n    },\n    encaissementsByPeriod: state => (debut, fin) => {\n      return state.encaissements.filter(e => {\n        const date = new Date(e.date);\n        return date >= debut && date <= fin;\n      });\n    },\n    \n    // Getters pour le module Matières et Examens\n    matieresSorted: state => {\n      return [...state.matieres].sort((a, b) => a.nom.localeCompare(b.nom));\n    },\n    \n    // Getters pour le module Notes\n    notesByEleve: state => (classeId, examenId, eleveId) => {\n      const key = `${classeId}_${examenId}`;\n      if (state.notes[key]) {\n        return state.notes[key][eleveId];\n      }\n      return null;\n    },\n    \n    // Getters pour les données partagées\n    elevesByClasse: state => classeId => {\n      return state.eleves.filter(e => e.classeId === classeId);\n    },\n    getEleveById: state => id => {\n      return state.eleves.find(e => e.id === id);\n    },\n    getClasseById: state => id => {\n      return state.classes.find(c => c.id === id);\n    },\n    \n    // Getter pour l'état de chargement\n    isLoading: state => state.isLoading\n  }\n})\n", "import Vue from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\n\n// Importer les services API\nimport * as ApiServices from './services/api'\n\n// Rendre les services API disponibles globalement\nVue.prototype.$api = ApiServices\n\nVue.config.productionTip = false\n\nnew Vue({\n  router,\n  store,\n  render: h => h(App)\n}).$mount('#app')\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = function(chunkId) {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce(function(promises, key) {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = function(chunkId) {\n\t// return url for filenames based on template\n\treturn \"js/\" + chunkId + \".\" + {\"130\":\"dda0c49a\",\"191\":\"fd9aa191\",\"275\":\"fe649778\",\"286\":\"7f82cf19\",\"450\":\"6c636c5b\",\"569\":\"0ac51137\",\"593\":\"e11e7061\",\"716\":\"51576e7c\",\"886\":\"2371e518\",\"936\":\"6cd8949e\",\"998\":\"1cf200da\"}[chunkId] + \".js\";\n};", "// This function allow to reference async chunks\n__webpack_require__.miniCssF = function(chunkId) {\n\t// return url for filenames based on template\n\treturn \"css/\" + chunkId + \".\" + {\"191\":\"6747c71f\",\"275\":\"9cc6ddc1\",\"286\":\"78c42326\",\"450\":\"c1d1b968\",\"569\":\"9fbaaf3c\",\"593\":\"03061211\",\"716\":\"a2014a71\",\"886\":\"e81da3e8\",\"936\":\"cf5ae710\",\"998\":\"42bec75a\"}[chunkId] + \".css\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "var inProgress = {};\nvar dataWebpackPrefix = \"projet_gestion_scolaire:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = function(url, done, key, chunkId) {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = function(prev, event) {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach(function(fn) { return fn(event); });\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.p = \"/\";", "if (typeof document === \"undefined\") return;\nvar createStylesheet = function(chunkId, fullhref, oldTag, resolve, reject) {\n\tvar linkTag = document.createElement(\"link\");\n\n\tlinkTag.rel = \"stylesheet\";\n\tlinkTag.type = \"text/css\";\n\tif (__webpack_require__.nc) {\n\t\tlinkTag.nonce = __webpack_require__.nc;\n\t}\n\tvar onLinkComplete = function(event) {\n\t\t// avoid mem leaks.\n\t\tlinkTag.onerror = linkTag.onload = null;\n\t\tif (event.type === 'load') {\n\t\t\tresolve();\n\t\t} else {\n\t\t\tvar errorType = event && event.type;\n\t\t\tvar realHref = event && event.target && event.target.href || fullhref;\n\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + errorType + \": \" + realHref + \")\");\n\t\t\terr.name = \"ChunkLoadError\";\n\t\t\terr.code = \"CSS_CHUNK_LOAD_FAILED\";\n\t\t\terr.type = errorType;\n\t\t\terr.request = realHref;\n\t\t\tif (linkTag.parentNode) linkTag.parentNode.removeChild(linkTag)\n\t\t\treject(err);\n\t\t}\n\t}\n\tlinkTag.onerror = linkTag.onload = onLinkComplete;\n\tlinkTag.href = fullhref;\n\n\n\tif (oldTag) {\n\t\toldTag.parentNode.insertBefore(linkTag, oldTag.nextSibling);\n\t} else {\n\t\tdocument.head.appendChild(linkTag);\n\t}\n\treturn linkTag;\n};\nvar findStylesheet = function(href, fullhref) {\n\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n\tfor(var i = 0; i < existingLinkTags.length; i++) {\n\t\tvar tag = existingLinkTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return tag;\n\t}\n\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n\tfor(var i = 0; i < existingStyleTags.length; i++) {\n\t\tvar tag = existingStyleTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\");\n\t\tif(dataHref === href || dataHref === fullhref) return tag;\n\t}\n};\nvar loadStylesheet = function(chunkId) {\n\treturn new Promise(function(resolve, reject) {\n\t\tvar href = __webpack_require__.miniCssF(chunkId);\n\t\tvar fullhref = __webpack_require__.p + href;\n\t\tif(findStylesheet(href, fullhref)) return resolve();\n\t\tcreateStylesheet(chunkId, fullhref, null, resolve, reject);\n\t});\n}\n// object to store loaded CSS chunks\nvar installedCssChunks = {\n\t524: 0\n};\n\n__webpack_require__.f.miniCss = function(chunkId, promises) {\n\tvar cssChunks = {\"191\":1,\"275\":1,\"286\":1,\"450\":1,\"569\":1,\"593\":1,\"716\":1,\"886\":1,\"936\":1,\"998\":1};\n\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n\t\tpromises.push(installedCssChunks[chunkId] = loadStylesheet(chunkId).then(function() {\n\t\t\tinstalledCssChunks[chunkId] = 0;\n\t\t}, function(e) {\n\t\t\tdelete installedCssChunks[chunkId];\n\t\t\tthrow e;\n\t\t}));\n\t}\n};\n\n// no hmr\n\n// no prefetching\n\n// no preloaded", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n__webpack_require__.f.j = function(chunkId, promises) {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise(function(resolve, reject) { installedChunkData = installedChunks[chunkId] = [resolve, reject]; });\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = function(event) {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkprojet_gestion_scolaire\"] = self[\"webpackChunkprojet_gestion_scolaire\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], function() { return __webpack_require__(8036); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_Header", "_createElementVNode", "_hoisted_2", "_component_Sidebar", "_hoisted_3", "_component_router_view", "_component_Footer", "_createCommentVNode", "name", "__exports__", "_component_router_link", "to", "exact", "default", "_withCtx", "_cache", "_createTextVNode", "_", "_hoisted_4", "_hoisted_5", "_toDisplayString", "$options", "currentYear", "computed", "Date", "getFullYear", "components", "Header", "Sidebar", "Footer", "created", "this", "$store", "dispatch", "render", "<PERSON><PERSON>", "use", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Dashboard", "FinanceEncaissement", "FinanceDecaissement", "FinanceSynthese", "MatieresList", "ExamensList", "NotesGestion", "BulletinsGeneration", "ArchivesBulletins", "StatistiquesResultats", "routes", "path", "component", "redirect", "router", "mode", "base", "process", "delay", "ms", "Promise", "resolve", "setTimeout", "ElevesService", "getAll", "id", "nom", "prenom", "matricule", "classeId", "getByClasse", "eleves", "filter", "eleve", "getById", "find", "ClassesService", "effectif", "classes", "classe", "MatieresService", "abreviation", "coefficient", "create", "matiere", "now", "update", "delete", "success", "ExamensService", "type", "dateDebut", "dateFin", "classesIds", "<PERSON><PERSON><PERSON><PERSON>", "examen", "toggleVerrouillage", "examens", "e", "Error", "NotesService", "getByClasseAndExamen", "matieres", "notes", "for<PERSON>ach", "ds1", "Math", "floor", "random", "ds2", "saveNotes", "message", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "examenId", "moy<PERSON>s", "totauxPonderes", "totalCoefficients", "reduce", "sum", "totalPoints", "eleveNotes", "moyenne", "parseFloat", "points", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "entries", "map", "eleveId", "parseInt", "sort", "a", "b", "rangs", "item", "index", "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "BulletinsService", "generer", "options", "then", "bulletins", "eleveName", "totalPondere", "rang", "dateGeneration", "toISOString", "getBulletinPDF", "bulletinId", "url", "FinanceService", "getEncaissements", "filters", "encaissements", "date", "montant", "motif", "modePaiement", "result", "searchTerm", "term", "toLowerCase", "el", "includes", "saveEncaissement", "encaissement", "getEncaissementRecu", "encaissementId", "getDecaissements", "decaissements", "beneficiaire", "justificatif", "d", "saveDecaissement", "decaissement", "getSynthese", "periode", "totalEncaissements", "totalDecaissements", "repartitionModes", "Cash", "ADRA", "motifs", "Set", "repartitionMotifs", "solde", "StatistiquesService", "getResultatsParMatiere", "moyennesParMatiere", "toFixed", "tauxReussite", "getEvolutionResultats", "trimestres", "getTopEleves", "limit", "topEleves", "slice", "min", "Vuex", "state", "currentUser", "role", "isAuthenticated", "isLoading", "anneeScolaire", "mutations", "SET_LOADING", "SET_ENCAISSEMENTS", "ADD_ENCAISSEMENT", "unshift", "SET_DECAISSEMENTS", "ADD_DECAISSEMENT", "SET_MATIERES", "ADD_MATIERE", "push", "UPDATE_MATIERE", "findIndex", "m", "splice", "DELETE_MATIERE", "SET_EXAMENS", "ADD_EXAMEN", "UPDATE_EXAMEN", "SET_NOTES", "set", "UPDATE_NOTE", "matiereId", "note", "key", "SET_BULLETINS", "ADD_BULLETIN", "bulletin", "SET_ELEVES", "SET_CLASSES", "SET_ANNEE_SCOLAIRE", "actions", "fetchEncaissements", "commit", "error", "console", "savedEncaissement", "fetchMatieres", "createMatiere", "savedMatiere", "updateMatiere", "updatedMatiere", "deleteMatiere", "fetchExamens", "createExamen", "savedExamen", "toggleExamenVerrouillage", "updatedExamen", "fetchNotes", "generateBulletins", "fetchEleves", "fetchClasses", "fetchStatistiquesParMatiere", "fetchEvolutionResultats", "getters", "total", "encaissementsByPeriod", "debut", "fin", "matieresSorted", "localeCompare", "notesByEleve", "elevesByClasse", "getEleveById", "getClasseById", "c", "prototype", "$api", "ApiServices", "config", "productionTip", "store", "h", "App", "$mount", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "__webpack_modules__", "call", "deferred", "O", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "i", "fulfilled", "j", "keys", "every", "r", "n", "getter", "__esModule", "definition", "o", "defineProperty", "enumerable", "get", "f", "chunkId", "all", "promises", "u", "miniCssF", "g", "globalThis", "Function", "window", "obj", "prop", "hasOwnProperty", "inProgress", "dataWebpackPrefix", "l", "done", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "document", "getElementsByTagName", "s", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "src", "onScriptComplete", "prev", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "bind", "target", "head", "append<PERSON><PERSON><PERSON>", "Symbol", "toStringTag", "value", "p", "createStylesheet", "fullhref", "oldTag", "reject", "linkTag", "rel", "nonce", "onLinkComplete", "errorType", "realHref", "href", "err", "code", "request", "insertBefore", "nextS<PERSON>ling", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "existingLinkTags", "tag", "dataHref", "existingStyleTags", "loadStylesheet", "installedCssChunks", "miniCss", "cssChunks", "installedChunks", "installedChunkData", "promise", "loadingEnded", "realSrc", "webpackJsonpCallback", "parentChunkLoadingFunction", "data", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "__webpack_exports__"], "sourceRoot": ""}