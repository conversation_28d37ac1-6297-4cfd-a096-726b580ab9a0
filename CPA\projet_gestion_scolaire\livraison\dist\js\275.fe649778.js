"use strict";(self["webpackChunkprojet_gestion_scolaire"]=self["webpackChunkprojet_gestion_scolaire"]||[]).push([[275],{9275:function(e,t,n){n.r(t),n.d(t,{default:function(){return A}});var a=n(6768),s=n(5130),i=n(4232);const o={class:"encaissement-container"},l={class:"card"},r={class:"form-group"},d={class:"form-group"},c=["value"],u={class:"form-group"},m={class:"form-group"},v={class:"form-group"},k={class:"card mt-4"},p={class:"search-form"},b={class:"form-group"},L={class:"form-group"},f={class:"form-group"},h={class:"form-actions"},E={class:"card mt-4"},g={class:"table-responsive"},D={class:"data-table"},F={key:0};function w(e,t,n,w,y,I){return(0,a.uX)(),(0,a.CE)("div",o,[t[29]||(t[29]=(0,a.Lk)("h1",null,"Gestion des Encaissements",-1)),(0,a.Lk)("div",l,[t[19]||(t[19]=(0,a.Lk)("h2",null,"Enregistrement d'un paiement",-1)),(0,a.Lk)("form",{onSubmit:t[5]||(t[5]=(0,s.D$)(((...e)=>I.saveEncaissement&&I.saveEncaissement(...e)),["prevent"])),class:"encaissement-form"},[(0,a.Lk)("div",r,[t[10]||(t[10]=(0,a.Lk)("label",{for:"date"},"Date",-1)),(0,a.bo)((0,a.Lk)("input",{type:"date",id:"date","onUpdate:modelValue":t[0]||(t[0]=e=>y.newEncaissement.date=e),required:""},null,512),[[s.Jo,y.newEncaissement.date]])]),(0,a.Lk)("div",d,[t[12]||(t[12]=(0,a.Lk)("label",{for:"eleve"},"Élève",-1)),(0,a.bo)((0,a.Lk)("select",{id:"eleve","onUpdate:modelValue":t[1]||(t[1]=e=>y.newEncaissement.eleveId=e),required:""},[t[11]||(t[11]=(0,a.Lk)("option",{value:"",disabled:""},"Sélectionner un élève",-1)),((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(y.eleves,(e=>((0,a.uX)(),(0,a.CE)("option",{key:e.id,value:e.id},(0,i.v_)(e.nom)+" "+(0,i.v_)(e.prenom)+" ("+(0,i.v_)(e.matricule)+") ",9,c)))),128))],512),[[s.u1,y.newEncaissement.eleveId]])]),(0,a.Lk)("div",u,[t[13]||(t[13]=(0,a.Lk)("label",{for:"montant"},"Montant (Ar)",-1)),(0,a.bo)((0,a.Lk)("input",{type:"number",id:"montant","onUpdate:modelValue":t[2]||(t[2]=e=>y.newEncaissement.montant=e),min:"0",required:""},null,512),[[s.Jo,y.newEncaissement.montant]])]),(0,a.Lk)("div",m,[t[15]||(t[15]=(0,a.Lk)("label",{for:"motif"},"Motif",-1)),(0,a.bo)((0,a.Lk)("select",{id:"motif","onUpdate:modelValue":t[3]||(t[3]=e=>y.newEncaissement.motif=e),required:""},t[14]||(t[14]=[(0,a.Fv)('<option value="" disabled data-v-4a1989be>Sélectionner un motif</option><option value="Frais de scolarité" data-v-4a1989be>Frais de scolarité</option><option value="Frais d&#39;examen" data-v-4a1989be>Frais d&#39;examen</option><option value="Frais d&#39;inscription" data-v-4a1989be>Frais d&#39;inscription</option><option value="Autre" data-v-4a1989be>Autre</option>',5)]),512),[[s.u1,y.newEncaissement.motif]])]),(0,a.Lk)("div",v,[t[17]||(t[17]=(0,a.Lk)("label",{for:"modePaiement"},"Mode de paiement",-1)),(0,a.bo)((0,a.Lk)("select",{id:"modePaiement","onUpdate:modelValue":t[4]||(t[4]=e=>y.newEncaissement.modePaiement=e),required:""},t[16]||(t[16]=[(0,a.Lk)("option",{value:"",disabled:""},"Sélectionner un mode",-1),(0,a.Lk)("option",{value:"Cash"},"Cash",-1),(0,a.Lk)("option",{value:"ADRA"},"ADRA",-1)]),512),[[s.u1,y.newEncaissement.modePaiement]])]),t[18]||(t[18]=(0,a.Lk)("div",{class:"form-actions"},[(0,a.Lk)("button",{type:"reset",class:"btn btn-secondary"},"Annuler"),(0,a.Lk)("button",{type:"submit",class:"btn btn-primary"},"Enregistrer")],-1))],32)]),(0,a.Lk)("div",k,[t[23]||(t[23]=(0,a.Lk)("h2",null,"Recherche avancée",-1)),(0,a.Lk)("div",p,[(0,a.Lk)("div",b,[t[20]||(t[20]=(0,a.Lk)("label",{for:"searchTerm"},"Recherche par matricule ou nom",-1)),(0,a.bo)((0,a.Lk)("input",{type:"text",id:"searchTerm","onUpdate:modelValue":t[6]||(t[6]=e=>y.searchTerm=e),placeholder:"Entrez un matricule ou un nom"},null,512),[[s.Jo,y.searchTerm]])]),(0,a.Lk)("div",L,[t[21]||(t[21]=(0,a.Lk)("label",{for:"dateDebut"},"Date début",-1)),(0,a.bo)((0,a.Lk)("input",{type:"date",id:"dateDebut","onUpdate:modelValue":t[7]||(t[7]=e=>y.dateDebut=e)},null,512),[[s.Jo,y.dateDebut]])]),(0,a.Lk)("div",f,[t[22]||(t[22]=(0,a.Lk)("label",{for:"dateFin"},"Date fin",-1)),(0,a.bo)((0,a.Lk)("input",{type:"date",id:"dateFin","onUpdate:modelValue":t[8]||(t[8]=e=>y.dateFin=e)},null,512),[[s.Jo,y.dateFin]])]),(0,a.Lk)("div",h,[(0,a.Lk)("button",{onClick:t[9]||(t[9]=(...e)=>I.searchEncaissements&&I.searchEncaissements(...e)),class:"btn btn-primary"},"Rechercher")])])]),(0,a.Lk)("div",E,[t[27]||(t[27]=(0,a.Lk)("h2",null,"Liste des encaissements",-1)),(0,a.Lk)("div",g,[(0,a.Lk)("table",D,[t[26]||(t[26]=(0,a.Lk)("thead",null,[(0,a.Lk)("tr",null,[(0,a.Lk)("th",null,"Date"),(0,a.Lk)("th",null,"Élève"),(0,a.Lk)("th",null,"Matricule"),(0,a.Lk)("th",null,"Montant (Ar)"),(0,a.Lk)("th",null,"Motif"),(0,a.Lk)("th",null,"Mode"),(0,a.Lk)("th",null,"Actions")])],-1)),(0,a.Lk)("tbody",null,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(y.encaissements,((e,n)=>((0,a.uX)(),(0,a.CE)("tr",{key:n},[(0,a.Lk)("td",null,(0,i.v_)(I.formatDate(e.date)),1),(0,a.Lk)("td",null,(0,i.v_)(I.getEleveName(e.eleveId)),1),(0,a.Lk)("td",null,(0,i.v_)(I.getEleveMatricule(e.eleveId)),1),(0,a.Lk)("td",null,(0,i.v_)(I.formatMontant(e.montant)),1),(0,a.Lk)("td",null,(0,i.v_)(e.motif),1),(0,a.Lk)("td",null,(0,i.v_)(e.modePaiement),1),t[24]||(t[24]=(0,a.Lk)("td",null,[(0,a.Lk)("button",{class:"btn-icon",title:"Imprimer reçu"},[(0,a.Lk)("i",{class:"fas fa-print"})]),(0,a.Lk)("button",{class:"btn-icon",title:"Voir détails"},[(0,a.Lk)("i",{class:"fas fa-eye"})])],-1))])))),128)),0===y.encaissements.length?((0,a.uX)(),(0,a.CE)("tr",F,t[25]||(t[25]=[(0,a.Lk)("td",{colspan:"7",class:"text-center"},"Aucun encaissement trouvé",-1)]))):(0,a.Q3)("",!0)])])]),t[28]||(t[28]=(0,a.Fv)('<div class="export-actions mt-3" data-v-4a1989be><button class="btn btn-secondary" data-v-4a1989be><i class="fas fa-file-excel" data-v-4a1989be></i> Export Excel </button><button class="btn btn-secondary" data-v-4a1989be><i class="fas fa-file-pdf" data-v-4a1989be></i> Export PDF </button></div>',1))])])}n(8111),n(116);var y={name:"FinanceEncaissement",data(){return{newEncaissement:{date:(new Date).toISOString().substr(0,10),eleveId:"",montant:"",motif:"",modePaiement:""},encaissements:[{id:1,date:"2024-03-15",eleveId:1,montant:5e4,motif:"Frais de scolarité",modePaiement:"Cash"},{id:2,date:"2024-03-20",eleveId:2,montant:25e3,motif:"Frais d'examen",modePaiement:"ADRA"}],eleves:[{id:1,nom:"Dupont",prenom:"Jean",matricule:"E001",classeId:1},{id:2,nom:"Martin",prenom:"Sophie",matricule:"E002",classeId:1}],searchTerm:"",dateDebut:"",dateFin:""}},methods:{saveEncaissement(){const e={id:Date.now(),...this.newEncaissement};this.encaissements.unshift(e),this.newEncaissement={date:(new Date).toISOString().substr(0,10),eleveId:"",montant:"",motif:"",modePaiement:""},alert("Encaissement enregistré avec succès !")},searchEncaissements(){alert("Recherche effectuée !")},formatDate(e){const t={day:"2-digit",month:"2-digit",year:"numeric"};return new Date(e).toLocaleDateString("fr-FR",t)},formatMontant(e){return new Intl.NumberFormat("fr-FR").format(e)},getEleveName(e){const t=this.eleves.find((t=>t.id===e));return t?`${t.nom} ${t.prenom}`:"Inconnu"},getEleveMatricule(e){const t=this.eleves.find((t=>t.id===e));return t?t.matricule:"Inconnu"}}},I=n(1241);const _=(0,I.A)(y,[["render",w],["__scopeId","data-v-4a1989be"]]);var A=_}}]);
//# sourceMappingURL=275.fe649778.js.map