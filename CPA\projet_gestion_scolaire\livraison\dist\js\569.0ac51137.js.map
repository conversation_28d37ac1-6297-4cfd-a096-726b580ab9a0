{"version": 3, "file": "js/569.0ac51137.js", "mappings": "iNACOA,MAAM,a,GAEJA,MAAM,kB,GACJA,MAAM,0B,GAYJA,MAAM,gB,GAKRA,MAAM,2B,GAYJA,MAAM,gB,GAKRA,MAAM,4B,GAYJA,MAAM,gB,GAKRA,MAAM,wB,GAYJA,MAAM,gB,0EAlEjBC,EAAAA,EAAAA,IAyFM,MAzFNC,EAyFM,cAxFJC,EAAAA,EAAAA,IAAwB,UAApB,mBAAe,KACnBA,EAAAA,EAAAA,IAoEM,MApENC,EAoEM,EAnEJD,EAAAA,EAAAA,IAeM,MAfNE,EAeM,cAnBZC,EAAAA,EAAAA,IAAA,saAgBQH,EAAAA,EAAAA,IAEM,MAFNI,EAEM,EADJC,EAAAA,EAAAA,IAA+EC,EAAA,CAAlEC,GAAG,yBAAyBV,MAAM,O,CAjBzDW,SAAAC,EAAAA,EAAAA,KAiB+D,IAAYC,EAAA,KAAAA,EAAA,KAjB3EC,EAAAA,EAAAA,IAiB+D,oBAjB/DC,EAAA,SAqBMZ,EAAAA,EAAAA,IAeM,MAfNa,EAeM,cApCZV,EAAAA,EAAAA,IAAA,6ZAiCQH,EAAAA,EAAAA,IAEM,MAFNc,EAEM,EADJT,EAAAA,EAAAA,IAA+DC,EAAA,CAAlDC,GAAG,SAASV,MAAM,O,CAlCzCW,SAAAC,EAAAA,EAAAA,KAkC+C,IAAYC,EAAA,KAAAA,EAAA,KAlC3DC,EAAAA,EAAAA,IAkC+C,oBAlC/CC,EAAA,SAsCMZ,EAAAA,EAAAA,IAeM,MAfNe,EAeM,cArDZZ,EAAAA,EAAAA,IAAA,oZAkDQH,EAAAA,EAAAA,IAEM,MAFNgB,EAEM,EADJX,EAAAA,EAAAA,IAAwEC,EAAA,CAA3DC,GAAG,aAAaV,MAAM,O,CAnD7CW,SAAAC,EAAAA,EAAAA,KAmDmD,IAAiBC,EAAA,KAAAA,EAAA,KAnDpEC,EAAAA,EAAAA,IAmDmD,yBAnDnDC,EAAA,SAuDMZ,EAAAA,EAAAA,IAeM,MAfNiB,EAeM,cAtEZd,EAAAA,EAAAA,IAAA,iaAmEQH,EAAAA,EAAAA,IAEM,MAFNkB,EAEM,EADJb,EAAAA,EAAAA,IAAuEC,EAAA,CAA1DC,GAAG,gBAAgBV,MAAM,O,CApEhDW,SAAAC,EAAAA,EAAAA,KAoEsD,IAAaC,EAAA,KAAAA,EAAA,KApEnEC,EAAAA,EAAAA,IAoEsD,qBApEtDC,EAAA,U,aAAAT,EAAAA,EAAAA,IAAA,0wB,CA8FA,OACEgB,KAAM,iB,UCxFR,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://projet_gestion_scolaire/./src/views/Dashboard.vue", "webpack://projet_gestion_scolaire/./src/views/Dashboard.vue?040e"], "sourcesContent": ["<template>\n  <div class=\"dashboard\">\n    <h1>Tableau de bord</h1>\n    <div class=\"dashboard-grid\">\n      <div class=\"dashboard-card finance\">\n        <h2>Finance</h2>\n        <div class=\"card-content\">\n          <div class=\"stat-item\">\n            <span class=\"stat-label\">Encaissements du mois</span>\n            <span class=\"stat-value\">1 250 000 Ar</span>\n          </div>\n          <div class=\"stat-item\">\n            <span class=\"stat-label\">Taux de paiement</span>\n            <span class=\"stat-value\">78%</span>\n          </div>\n        </div>\n        <div class=\"card-actions\">\n          <router-link to=\"/finance/encaissements\" class=\"btn\">Voir détails</router-link>\n        </div>\n      </div>\n      \n      <div class=\"dashboard-card academic\">\n        <h2>Académique</h2>\n        <div class=\"card-content\">\n          <div class=\"stat-item\">\n            <span class=\"stat-label\">Examens en cours</span>\n            <span class=\"stat-value\">2</span>\n          </div>\n          <div class=\"stat-item\">\n            <span class=\"stat-label\">Notes à saisir</span>\n            <span class=\"stat-value\">3 classes</span>\n          </div>\n        </div>\n        <div class=\"card-actions\">\n          <router-link to=\"/notes\" class=\"btn\">Saisir notes</router-link>\n        </div>\n      </div>\n      \n      <div class=\"dashboard-card bulletins\">\n        <h2>Bulletins</h2>\n        <div class=\"card-content\">\n          <div class=\"stat-item\">\n            <span class=\"stat-label\">Bulletins générés</span>\n            <span class=\"stat-value\">125</span>\n          </div>\n          <div class=\"stat-item\">\n            <span class=\"stat-label\">En attente</span>\n            <span class=\"stat-value\">45</span>\n          </div>\n        </div>\n        <div class=\"card-actions\">\n          <router-link to=\"/bulletins\" class=\"btn\">Générer bulletins</router-link>\n        </div>\n      </div>\n      \n      <div class=\"dashboard-card stats\">\n        <h2>Statistiques</h2>\n        <div class=\"card-content\">\n          <div class=\"stat-item\">\n            <span class=\"stat-label\">Moyenne générale</span>\n            <span class=\"stat-value\">12.8/20</span>\n          </div>\n          <div class=\"stat-item\">\n            <span class=\"stat-label\">Taux de réussite</span>\n            <span class=\"stat-value\">65%</span>\n          </div>\n        </div>\n        <div class=\"card-actions\">\n          <router-link to=\"/statistiques\" class=\"btn\">Voir analyses</router-link>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"recent-activity\">\n      <h2>Activités récentes</h2>\n      <ul class=\"activity-list\">\n        <li class=\"activity-item\">\n          <span class=\"activity-time\">Aujourd'hui, 10:45</span>\n          <span class=\"activity-desc\">Paiement enregistré pour Rakoto Jean (6ème A)</span>\n        </li>\n        <li class=\"activity-item\">\n          <span class=\"activity-time\">Hier, 15:30</span>\n          <span class=\"activity-desc\">Notes du Trimestre 1 validées pour la classe 5ème B</span>\n        </li>\n        <li class=\"activity-item\">\n          <span class=\"activity-time\">Hier, 09:15</span>\n          <span class=\"activity-desc\">25 bulletins générés pour la classe 4ème A</span>\n        </li>\n      </ul>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'DashboardView'\n}\n</script>\n\n<style scoped>\n.dashboard {\n  padding: 1.5rem;\n}\n\nh1 {\n  margin-bottom: 1.5rem;\n  color: #333;\n}\n\n.dashboard-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.dashboard-card {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  padding: 1.5rem;\n}\n\n.dashboard-card h2 {\n  margin-top: 0;\n  margin-bottom: 1rem;\n  font-size: 1.2rem;\n  color: #333;\n  border-bottom: 1px solid #eee;\n  padding-bottom: 0.5rem;\n}\n\n.card-content {\n  margin-bottom: 1rem;\n}\n\n.stat-item {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 0.5rem;\n}\n\n.stat-label {\n  color: #757575;\n}\n\n.stat-value {\n  font-weight: bold;\n  color: #333;\n}\n\n.card-actions {\n  text-align: right;\n}\n\n.btn {\n  display: inline-block;\n  background-color: #3f51b5;\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 4px;\n  text-decoration: none;\n  font-size: 0.9rem;\n  transition: background-color 0.3s;\n}\n\n.btn:hover {\n  background-color: #303f9f;\n}\n\n.finance {\n  border-top: 3px solid #4caf50;\n}\n\n.academic {\n  border-top: 3px solid #2196f3;\n}\n\n.bulletins {\n  border-top: 3px solid #ff9800;\n}\n\n.stats {\n  border-top: 3px solid #9c27b0;\n}\n\n.recent-activity {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  padding: 1.5rem;\n}\n\n.recent-activity h2 {\n  margin-top: 0;\n  margin-bottom: 1rem;\n  font-size: 1.2rem;\n  color: #333;\n  border-bottom: 1px solid #eee;\n  padding-bottom: 0.5rem;\n}\n\n.activity-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.activity-item {\n  padding: 0.75rem 0;\n  border-bottom: 1px solid #f5f5f5;\n  display: flex;\n  flex-direction: column;\n}\n\n.activity-time {\n  font-size: 0.8rem;\n  color: #757575;\n  margin-bottom: 0.25rem;\n}\n\n.activity-desc {\n  color: #333;\n}\n</style>\n", "import { render } from \"./Dashboard.vue?vue&type=template&id=298cfa72&scoped=true\"\nimport script from \"./Dashboard.vue?vue&type=script&lang=js\"\nexport * from \"./Dashboard.vue?vue&type=script&lang=js\"\n\nimport \"./Dashboard.vue?vue&type=style&index=0&id=298cfa72&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-298cfa72\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createStaticVNode", "_hoisted_4", "_createVNode", "_component_router_link", "to", "default", "_withCtx", "_cache", "_createTextVNode", "_", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "name", "__exports__", "render"], "sourceRoot": ""}