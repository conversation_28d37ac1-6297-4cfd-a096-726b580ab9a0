"use strict";(self["webpackChunkprojet_gestion_scolaire"]=self["webpackChunkprojet_gestion_scolaire"]||[]).push([[450],{9450:function(e,a,l){l.r(a),l.d(a,{default:function(){return x}});var t=l(6768),s=l(5130),i=l(4232);const c={class:"archives-container"},d={class:"card"},n={class:"search-form"},o={class:"form-group"},r={class:"form-group"},v=["value"],u={class:"form-group"},b={class:"form-group"},f={class:"form-actions"},m={key:0,class:"card mt-4"},h={class:"table-responsive"},k={class:"data-table"},p=["onClick"],L=["onClick"],g=["onClick"],E={class:"export-actions mt-3"},C={key:1,class:"card mt-4"};function T(e,a,l,T,A,y){return(0,t.uX)(),(0,t.CE)("div",c,[a[22]||(a[22]=(0,t.Lk)("h1",null,"Archives des Bulletins",-1)),(0,t.Lk)("div",d,[a[14]||(a[14]=(0,t.Lk)("h2",null,"Recherche d'archives",-1)),(0,t.Lk)("div",n,[(0,t.Lk)("div",o,[a[7]||(a[7]=(0,t.Lk)("label",{for:"anneeScolaire"},"Année scolaire",-1)),(0,t.bo)((0,t.Lk)("select",{id:"anneeScolaire","onUpdate:modelValue":a[0]||(a[0]=e=>A.selectedAnneeScolaire=e)},a[6]||(a[6]=[(0,t.Lk)("option",{value:"2024-2025"},"2024-2025",-1),(0,t.Lk)("option",{value:"2023-2024"},"2023-2024",-1),(0,t.Lk)("option",{value:"2022-2023"},"2022-2023",-1)]),512),[[s.u1,A.selectedAnneeScolaire]])]),(0,t.Lk)("div",r,[a[9]||(a[9]=(0,t.Lk)("label",{for:"classe"},"Classe",-1)),(0,t.bo)((0,t.Lk)("select",{id:"classe","onUpdate:modelValue":a[1]||(a[1]=e=>A.selectedClasseId=e)},[a[8]||(a[8]=(0,t.Lk)("option",{value:""},"Toutes les classes",-1)),((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(A.classes,(e=>((0,t.uX)(),(0,t.CE)("option",{key:e.id,value:e.id},(0,i.v_)(e.nom),9,v)))),128))],512),[[s.u1,A.selectedClasseId]])]),(0,t.Lk)("div",u,[a[11]||(a[11]=(0,t.Lk)("label",{for:"examen"},"Examen",-1)),(0,t.bo)((0,t.Lk)("select",{id:"examen","onUpdate:modelValue":a[2]||(a[2]=e=>A.selectedExamenType=e)},a[10]||(a[10]=[(0,t.Fv)('<option value="" data-v-09b4b67c>Tous les examens</option><option value="Trimestre 1" data-v-09b4b67c>Trimestre 1</option><option value="Trimestre 2" data-v-09b4b67c>Trimestre 2</option><option value="Trimestre 3" data-v-09b4b67c>Trimestre 3</option><option value="Examen Final" data-v-09b4b67c>Examen Final</option>',5)]),512),[[s.u1,A.selectedExamenType]])]),(0,t.Lk)("div",b,[a[12]||(a[12]=(0,t.Lk)("label",{for:"searchEleve"},"Recherche par élève",-1)),(0,t.bo)((0,t.Lk)("input",{type:"text",id:"searchEleve","onUpdate:modelValue":a[3]||(a[3]=e=>A.searchEleve=e),placeholder:"Nom ou matricule de l'élève"},null,512),[[s.Jo,A.searchEleve]])]),(0,t.Lk)("div",f,[(0,t.Lk)("button",{onClick:a[4]||(a[4]=(...e)=>y.searchArchives&&y.searchArchives(...e)),class:"btn btn-primary"},a[13]||(a[13]=[(0,t.Lk)("i",{class:"fas fa-search"},null,-1),(0,t.eW)(" Rechercher ")]))])])]),A.archives.length>0?((0,t.uX)(),(0,t.CE)("div",m,[a[20]||(a[20]=(0,t.Lk)("h2",null,"Résultats de la recherche",-1)),(0,t.Lk)("div",h,[(0,t.Lk)("table",k,[a[18]||(a[18]=(0,t.Lk)("thead",null,[(0,t.Lk)("tr",null,[(0,t.Lk)("th",null,"Élève"),(0,t.Lk)("th",null,"Matricule"),(0,t.Lk)("th",null,"Classe"),(0,t.Lk)("th",null,"Examen"),(0,t.Lk)("th",null,"Date de génération"),(0,t.Lk)("th",null,"Actions")])],-1)),(0,t.Lk)("tbody",null,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(A.archives,((e,l)=>((0,t.uX)(),(0,t.CE)("tr",{key:l},[(0,t.Lk)("td",null,(0,i.v_)(e.eleveName),1),(0,t.Lk)("td",null,(0,i.v_)(e.matricule),1),(0,t.Lk)("td",null,(0,i.v_)(e.classe),1),(0,t.Lk)("td",null,(0,i.v_)(e.examen),1),(0,t.Lk)("td",null,(0,i.v_)(y.formatDate(e.dateGeneration)),1),(0,t.Lk)("td",null,[(0,t.Lk)("button",{onClick:a=>y.viewBulletin(e),class:"btn-icon",title:"Voir bulletin"},a[15]||(a[15]=[(0,t.Lk)("i",{class:"fas fa-eye"},null,-1)]),8,p),(0,t.Lk)("button",{onClick:a=>y.downloadBulletin(e),class:"btn-icon",title:"Télécharger"},a[16]||(a[16]=[(0,t.Lk)("i",{class:"fas fa-download"},null,-1)]),8,L),(0,t.Lk)("button",{onClick:a=>y.printBulletin(e),class:"btn-icon",title:"Imprimer"},a[17]||(a[17]=[(0,t.Lk)("i",{class:"fas fa-print"},null,-1)]),8,g)])])))),128))])])]),(0,t.Lk)("div",E,[(0,t.Lk)("button",{onClick:a[5]||(a[5]=(...e)=>y.downloadAllBulletins&&y.downloadAllBulletins(...e)),class:"btn btn-secondary"},a[19]||(a[19]=[(0,t.Lk)("i",{class:"fas fa-download"},null,-1),(0,t.eW)(" Télécharger tous les bulletins ")]))])])):(0,t.Q3)("",!0),A.showCloudStorage?((0,t.uX)(),(0,t.CE)("div",C,a[21]||(a[21]=[(0,t.Fv)('<h2 data-v-09b4b67c>Stockage Cloud</h2><div class="cloud-storage" data-v-09b4b67c><div class="storage-info" data-v-09b4b67c><div class="storage-icon" data-v-09b4b67c><i class="fas fa-cloud" data-v-09b4b67c></i></div><div class="storage-details" data-v-09b4b67c><h3 data-v-09b4b67c>Espace de stockage</h3><div class="storage-bar" data-v-09b4b67c><div class="storage-used" style="width:35%;" data-v-09b4b67c></div></div><p data-v-09b4b67c>1.75 GB utilisés sur 5 GB</p></div></div><div class="storage-actions" data-v-09b4b67c><button class="btn btn-primary" data-v-09b4b67c><i class="fas fa-sync-alt" data-v-09b4b67c></i> Synchroniser </button><button class="btn btn-secondary" data-v-09b4b67c><i class="fas fa-cog" data-v-09b4b67c></i> Paramètres </button></div></div><div class="folder-structure mt-3" data-v-09b4b67c><h3 data-v-09b4b67c>Structure des dossiers</h3><div class="folder-tree" data-v-09b4b67c><div class="folder-item" data-v-09b4b67c><i class="fas fa-folder" data-v-09b4b67c></i> Archives <div class="folder-children" data-v-09b4b67c><div class="folder-item" data-v-09b4b67c><i class="fas fa-folder" data-v-09b4b67c></i> 2024-2025 <div class="folder-children" data-v-09b4b67c><div class="folder-item" data-v-09b4b67c><i class="fas fa-folder" data-v-09b4b67c></i> 6ème A </div><div class="folder-item" data-v-09b4b67c><i class="fas fa-folder" data-v-09b4b67c></i> 6ème B </div><div class="folder-item" data-v-09b4b67c><i class="fas fa-folder" data-v-09b4b67c></i> 5ème A </div><div class="folder-item" data-v-09b4b67c><i class="fas fa-folder" data-v-09b4b67c></i> 5ème B </div></div></div><div class="folder-item" data-v-09b4b67c><i class="fas fa-folder" data-v-09b4b67c></i> 2023-2024 </div><div class="folder-item" data-v-09b4b67c><i class="fas fa-folder" data-v-09b4b67c></i> 2022-2023 </div></div></div></div></div>',3)]))):(0,t.Q3)("",!0)])}var A={name:"ArchivesBulletins",data(){return{selectedAnneeScolaire:"2024-2025",selectedClasseId:"",selectedExamenType:"",searchEleve:"",showCloudStorage:!0,classes:[{id:1,nom:"6ème A",effectif:35},{id:2,nom:"6ème B",effectif:32},{id:3,nom:"5ème A",effectif:30},{id:4,nom:"5ème B",effectif:28}],archives:[{id:1,eleveName:"Dupont Jean",matricule:"E001",classe:"6ème A",examen:"Trimestre 1",dateGeneration:"2024-11-15T10:30:00",url:"/bulletins/E001_T1.pdf"},{id:2,eleveName:"Martin Sophie",matricule:"E002",classe:"6ème A",examen:"Trimestre 1",dateGeneration:"2024-11-15T10:35:00",url:"/bulletins/E002_T1.pdf"},{id:3,eleveName:"Dubois Pierre",matricule:"E003",classe:"6ème A",examen:"Trimestre 1",dateGeneration:"2024-11-15T10:40:00",url:"/bulletins/E003_T1.pdf"}]}},methods:{searchArchives(){alert("Recherche effectuée !")},viewBulletin(e){alert(`Affichage du bulletin: ${e.url}`)},downloadBulletin(e){alert(`Téléchargement du bulletin: ${e.url}`)},printBulletin(e){alert(`Impression du bulletin: ${e.url}`)},downloadAllBulletins(){alert(`Téléchargement de ${this.archives.length} bulletins`)},formatDate(e){const a={day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"};return new Date(e).toLocaleDateString("fr-FR",a)}}},y=l(1241);const B=(0,y.A)(A,[["render",T],["__scopeId","data-v-09b4b67c"]]);var x=B}}]);
//# sourceMappingURL=450.6c636c5b.js.map