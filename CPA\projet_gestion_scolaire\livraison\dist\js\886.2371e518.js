"use strict";(self["webpackChunkprojet_gestion_scolaire"]=self["webpackChunkprojet_gestion_scolaire"]||[]).push([[886],{2886:function(e,s,a){a.r(s),a.d(s,{default:function(){return N}});var t=a(6768),n=a(4232),l=a(5130);const i={class:"notes-container"},o={class:"card"},d={class:"selection-form"},r={class:"form-group"},c=["value"],u={class:"form-group"},m=["value"],v={class:"form-actions"},h=["disabled"],f={key:0,class:"card mt-4"},p={class:"table-responsive"},b={class:"data-table notes-table"},k={class:"sticky-col"},L=["onUpdate:modelValue","disabled"],x=["onUpdate:modelValue","disabled"],E=["onUpdate:modelValue","disabled"],y={class:"moyenne"},C={class:"rang"},I={class:"form-actions mt-3"},g=["disabled"],D={key:1,class:"alert alert-warning mt-4"};function F(e,s,a,F,V,_){return(0,t.uX)(),(0,t.CE)("div",i,[s[21]||(s[21]=(0,t.Lk)("h1",null,"Saisie des Notes",-1)),(0,t.Lk)("div",o,[s[12]||(s[12]=(0,t.Lk)("h2",null,"Sélection de l'examen et de la classe",-1)),(0,t.Lk)("div",d,[(0,t.Lk)("div",r,[s[9]||(s[9]=(0,t.Lk)("label",{for:"examen"},"Examen",-1)),(0,t.bo)((0,t.Lk)("select",{id:"examen","onUpdate:modelValue":s[0]||(s[0]=e=>V.selectedExamenId=e),required:""},[s[8]||(s[8]=(0,t.Lk)("option",{value:"",disabled:""},"Sélectionner un examen",-1)),((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(V.examens,(e=>((0,t.uX)(),(0,t.CE)("option",{key:e.id,value:e.id},(0,n.v_)(e.type)+" ("+(0,n.v_)(_.formatDate(e.dateDebut))+" - "+(0,n.v_)(_.formatDate(e.dateFin))+") ",9,c)))),128))],512),[[l.u1,V.selectedExamenId]])]),(0,t.Lk)("div",u,[s[11]||(s[11]=(0,t.Lk)("label",{for:"classe"},"Classe",-1)),(0,t.bo)((0,t.Lk)("select",{id:"classe","onUpdate:modelValue":s[1]||(s[1]=e=>V.selectedClasseId=e),required:""},[s[10]||(s[10]=(0,t.Lk)("option",{value:"",disabled:""},"Sélectionner une classe",-1)),((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(_.classesForExamen,(e=>((0,t.uX)(),(0,t.CE)("option",{key:e.id,value:e.id},(0,n.v_)(e.nom)+" ("+(0,n.v_)(e.effectif)+" élèves) ",9,m)))),128))],512),[[l.u1,V.selectedClasseId]])]),(0,t.Lk)("div",v,[(0,t.Lk)("button",{onClick:s[2]||(s[2]=(...e)=>_.loadNotes&&_.loadNotes(...e)),class:"btn btn-primary",disabled:!_.canLoadNotes}," Charger les notes ",8,h)])])]),V.notesLoaded?((0,t.uX)(),(0,t.CE)("div",f,[s[19]||(s[19]=(0,t.Fv)('<div class="card-header-actions" data-v-67544b00><h2 data-v-67544b00>Tableau de saisie des notes</h2><div class="header-actions" data-v-67544b00><button class="btn btn-secondary" data-v-67544b00><i class="fas fa-file-excel" data-v-67544b00></i> Importer Excel </button><button class="btn btn-secondary" data-v-67544b00><i class="fas fa-file-export" data-v-67544b00></i> Exporter </button></div></div>',1)),(0,t.Lk)("div",p,[(0,t.Lk)("table",b,[(0,t.Lk)("thead",null,[(0,t.Lk)("tr",null,[s[13]||(s[13]=(0,t.Lk)("th",{rowspan:"2",class:"sticky-col"},"Élève",-1)),((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(V.matieres,(e=>((0,t.uX)(),(0,t.CE)("th",{key:e.id,colspan:3,class:"matiere-header"},(0,n.v_)(e.nom)+" (Coef. "+(0,n.v_)(e.coefficient)+") ",1)))),128)),s[14]||(s[14]=(0,t.Lk)("th",{rowspan:"2"},"Moyenne",-1)),s[15]||(s[15]=(0,t.Lk)("th",{rowspan:"2"},"Rang",-1))]),(0,t.Lk)("tr",null,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(V.matieres,(e=>((0,t.uX)(),(0,t.CE)(t.FK,{key:`sub-${e.id}`},[s[16]||(s[16]=(0,t.Lk)("th",null,"DS1",-1)),s[17]||(s[17]=(0,t.Lk)("th",null,"DS2",-1)),s[18]||(s[18]=(0,t.Lk)("th",null,"Examen",-1))],64)))),128))])]),(0,t.Lk)("tbody",null,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(V.eleves,(e=>((0,t.uX)(),(0,t.CE)("tr",{key:e.id},[(0,t.Lk)("td",k,(0,n.v_)(e.nom)+" "+(0,n.v_)(e.prenom),1),((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(V.matieres,(a=>((0,t.uX)(),(0,t.CE)(t.FK,{key:`note-${e.id}-${a.id}`},[(0,t.Lk)("td",null,[(0,t.bo)((0,t.Lk)("input",{type:"number",min:"0",max:"20",step:"0.25","onUpdate:modelValue":s=>V.notesData[e.id][a.id].ds1=s,onChange:s[3]||(s[3]=e=>_.calculateAverages()),disabled:V.examenVerrouille},null,40,L),[[l.Jo,V.notesData[e.id][a.id].ds1]])]),(0,t.Lk)("td",null,[(0,t.bo)((0,t.Lk)("input",{type:"number",min:"0",max:"20",step:"0.25","onUpdate:modelValue":s=>V.notesData[e.id][a.id].ds2=s,onChange:s[4]||(s[4]=e=>_.calculateAverages()),disabled:V.examenVerrouille},null,40,x),[[l.Jo,V.notesData[e.id][a.id].ds2]])]),(0,t.Lk)("td",null,[(0,t.bo)((0,t.Lk)("input",{type:"number",min:"0",max:"20",step:"0.25","onUpdate:modelValue":s=>V.notesData[e.id][a.id].examen=s,onChange:s[5]||(s[5]=e=>_.calculateAverages()),disabled:V.examenVerrouille},null,40,E),[[l.Jo,V.notesData[e.id][a.id].examen]])])],64)))),128)),(0,t.Lk)("td",y,(0,n.v_)(V.moyennes[e.id]?V.moyennes[e.id].toFixed(2):"-"),1),(0,t.Lk)("td",C,(0,n.v_)(V.rangs[e.id]||"-"),1)])))),128))])])]),(0,t.Lk)("div",I,[(0,t.Lk)("button",{onClick:s[6]||(s[6]=(...e)=>_.saveNotes&&_.saveNotes(...e)),class:"btn btn-primary",disabled:V.examenVerrouille}," Enregistrer les notes ",8,g),V.examenVerrouille?(0,t.Q3)("",!0):((0,t.uX)(),(0,t.CE)("button",{key:0,onClick:s[7]||(s[7]=(...e)=>_.validateNotes&&_.validateNotes(...e)),class:"btn btn-success"}," Valider et verrouiller "))])])):(0,t.Q3)("",!0),V.examenVerrouille&&V.notesLoaded?((0,t.uX)(),(0,t.CE)("div",D,s[20]||(s[20]=[(0,t.Lk)("i",{class:"fas fa-lock"},null,-1),(0,t.eW)(" Cet examen est verrouillé. Les notes ne peuvent plus être modifiées. ")]))):(0,t.Q3)("",!0)])}a(8111),a(2489),a(116),a(7588),a(1701);var V={name:"NotesGestion",data(){return{selectedExamenId:"",selectedClasseId:"",notesLoaded:!1,examenVerrouille:!1,examens:[{id:1,type:"Trimestre 1",dateDebut:"2024-09-15",dateFin:"2024-10-30",classesIds:[1,2],verrouille:!0},{id:2,type:"Trimestre 1",dateDebut:"2024-09-15",dateFin:"2024-10-30",classesIds:[3,4],verrouille:!1}],classes:[{id:1,nom:"6ème A",effectif:35},{id:2,nom:"6ème B",effectif:32},{id:3,nom:"5ème A",effectif:30},{id:4,nom:"5ème B",effectif:28}],matieres:[{id:1,nom:"Mathématiques",abreviation:"MATH",coefficient:4},{id:2,nom:"Physique",abreviation:"PHY",coefficient:3},{id:3,nom:"Français",abreviation:"FR",coefficient:3}],eleves:[{id:1,nom:"Dupont",prenom:"Jean",matricule:"E001",classeId:1},{id:2,nom:"Martin",prenom:"Sophie",matricule:"E002",classeId:1},{id:3,nom:"Dubois",prenom:"Pierre",matricule:"E003",classeId:1}],notesData:{},moyennes:{},rangs:{}}},computed:{classesForExamen(){if(!this.selectedExamenId)return[];const e=this.examens.find((e=>e.id===this.selectedExamenId));return e?this.classes.filter((s=>e.classesIds.includes(s.id))):[]},canLoadNotes(){return this.selectedExamenId&&this.selectedClasseId}},methods:{loadNotes(){const e=this.examens.find((e=>e.id===this.selectedExamenId));this.examenVerrouille=!!e&&e.verrouille,this.eleves=[{id:1,nom:"Dupont",prenom:"Jean",matricule:"E001",classeId:this.selectedClasseId},{id:2,nom:"Martin",prenom:"Sophie",matricule:"E002",classeId:this.selectedClasseId},{id:3,nom:"Dubois",prenom:"Pierre",matricule:"E003",classeId:this.selectedClasseId}],this.notesData={},this.eleves.forEach((e=>{this.notesData[e.id]={},this.matieres.forEach((s=>{this.notesData[e.id][s.id]={ds1:Math.floor(10*Math.random())+10,ds2:Math.floor(10*Math.random())+10,examen:Math.floor(10*Math.random())+10}}))})),this.calculateAverages(),this.notesLoaded=!0},calculateAverages(){this.moyennes={},this.eleves.forEach((e=>{let s=0,a=0;this.matieres.forEach((t=>{const n=this.notesData[e.id][t.id],l=(parseFloat(n.ds1)+parseFloat(n.ds2)+parseFloat(n.examen))/3,i=l*t.coefficient;s+=i,a+=t.coefficient})),this.moyennes[e.id]=s/a}));const e=Object.entries(this.moyennes).map((([e,s])=>({eleveId:parseInt(e),moyenne:s}))).sort(((e,s)=>s.moyenne-e.moyenne));this.rangs={},e.forEach(((e,s)=>{this.rangs[e.eleveId]=s+1}))},saveNotes(){alert("Notes enregistrées avec succès !")},validateNotes(){if(confirm("Êtes-vous sûr de vouloir valider et verrouiller ces notes ? Cette action est irréversible.")){const e=this.examens.find((e=>e.id===this.selectedExamenId));e&&(e.verrouille=!0,this.examenVerrouille=!0),alert("Notes validées et verrouillées avec succès !")}},formatDate(e){const s={day:"2-digit",month:"2-digit",year:"numeric"};return new Date(e).toLocaleDateString("fr-FR",s)}}},_=a(1241);const X=(0,_.A)(V,[["render",F],["__scopeId","data-v-67544b00"]]);var N=X}}]);
//# sourceMappingURL=886.2371e518.js.map