"use strict";(self["webpackChunkprojet_gestion_scolaire"]=self["webpackChunkprojet_gestion_scolaire"]||[]).push([[998],{998:function(e,a,d){d.r(a),d.d(a,{default:function(){return X}});var s=d(6768),t=d(4232),i=d(5130);const l={class:"statistiques-container"},c={class:"card"},n={class:"filters-form"},v={class:"form-group"},r=["value"],o={class:"form-group"},u=["value"],m={class:"form-actions"},b=["disabled"],k={key:0,class:"stats-results"},g={class:"card mt-4"},f={class:"chart-container"},y={class:"bar-chart"},h={class:"bar-label"},p={class:"card mt-4"},L={class:"chart-container"},x={class:"bar-chart"},C={class:"bar-label"},E={class:"card mt-4"},_={class:"table-responsive"},I={class:"data-table"};function F(e,a,d,F,D,R){return(0,s.uX)(),(0,s.CE)("div",l,[a[14]||(a[14]=(0,s.Lk)("h1",null,"Statistiques des Résultats",-1)),(0,s.Lk)("div",c,[a[7]||(a[7]=(0,s.Lk)("h2",null,"Filtres",-1)),(0,s.Lk)("div",n,[(0,s.Lk)("div",v,[a[4]||(a[4]=(0,s.Lk)("label",{for:"examen"},"Examen",-1)),(0,s.bo)((0,s.Lk)("select",{id:"examen","onUpdate:modelValue":a[0]||(a[0]=e=>D.selectedExamenId=e)},[a[3]||(a[3]=(0,s.Lk)("option",{value:"",disabled:""},"Sélectionner un examen",-1)),((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(D.examens,(e=>((0,s.uX)(),(0,s.CE)("option",{key:e.id,value:e.id},(0,t.v_)(e.type)+" ("+(0,t.v_)(R.formatDate(e.dateDebut))+" - "+(0,t.v_)(R.formatDate(e.dateFin))+") ",9,r)))),128))],512),[[i.u1,D.selectedExamenId]])]),(0,s.Lk)("div",o,[a[6]||(a[6]=(0,s.Lk)("label",{for:"classe"},"Classe",-1)),(0,s.bo)((0,s.Lk)("select",{id:"classe","onUpdate:modelValue":a[1]||(a[1]=e=>D.selectedClasseId=e)},[a[5]||(a[5]=(0,s.Lk)("option",{value:"",disabled:""},"Sélectionner une classe",-1)),((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(D.classes,(e=>((0,s.uX)(),(0,s.CE)("option",{key:e.id,value:e.id},(0,t.v_)(e.nom),9,u)))),128))],512),[[i.u1,D.selectedClasseId]])]),(0,s.Lk)("div",m,[(0,s.Lk)("button",{onClick:a[2]||(a[2]=(...e)=>R.generateStatistiques&&R.generateStatistiques(...e)),class:"btn btn-primary",disabled:!R.canGenerateStats}," Générer les statistiques ",8,b)])])]),D.statsGenerated?((0,s.uX)(),(0,s.CE)("div",k,[(0,s.Lk)("div",g,[a[8]||(a[8]=(0,s.Lk)("h2",null,"Moyennes par matière",-1)),(0,s.Lk)("div",f,[(0,s.Lk)("div",y,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(D.moyennesParMatiere,((e,a)=>((0,s.uX)(),(0,s.CE)("div",{key:a,class:"bar-item"},[(0,s.Lk)("div",h,(0,t.v_)(e.matiere),1),(0,s.Lk)("div",{class:"bar-value",style:(0,t.Tr)({width:5*e.moyenne+"%"})},(0,t.v_)(e.moyenne)+"/20",5)])))),128))])])]),(0,s.Lk)("div",p,[a[9]||(a[9]=(0,s.Lk)("h2",null,"Taux de réussite par matière",-1)),(0,s.Lk)("div",L,[(0,s.Lk)("div",x,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(D.moyennesParMatiere,((e,a)=>((0,s.uX)(),(0,s.CE)("div",{key:a,class:"bar-item"},[(0,s.Lk)("div",C,(0,t.v_)(e.matiere),1),(0,s.Lk)("div",{class:"bar-value success",style:(0,t.Tr)({width:`${e.tauxReussite}%`})},(0,t.v_)(e.tauxReussite)+"%",5)])))),128))])])]),a[12]||(a[12]=(0,s.Fv)('<div class="card mt-4" data-v-963c08d0><h2 data-v-963c08d0>Évolution des résultats</h2><div class="chart-container" data-v-963c08d0><div class="line-chart-placeholder" data-v-963c08d0><div class="line-chart" data-v-963c08d0><div class="chart-legend" data-v-963c08d0><div class="legend-item" data-v-963c08d0><div class="legend-color" style="background-color:#3f51b5;" data-v-963c08d0></div><div class="legend-label" data-v-963c08d0>Moyenne générale</div></div><div class="legend-item" data-v-963c08d0><div class="legend-color" style="background-color:#4caf50;" data-v-963c08d0></div><div class="legend-label" data-v-963c08d0>Taux de réussite</div></div></div><div class="chart-grid" data-v-963c08d0><div class="chart-line" data-v-963c08d0><div class="point" data-v-963c08d0></div><div class="point" data-v-963c08d0></div><div class="point" data-v-963c08d0></div></div><div class="chart-line" data-v-963c08d0><div class="point" data-v-963c08d0></div><div class="point" data-v-963c08d0></div><div class="point" data-v-963c08d0></div></div><div class="x-axis" data-v-963c08d0><div class="tick" data-v-963c08d0>Trimestre 1</div><div class="tick" data-v-963c08d0>Trimestre 2</div><div class="tick" data-v-963c08d0>Trimestre 3</div></div></div></div></div></div></div>',1)),(0,s.Lk)("div",E,[a[11]||(a[11]=(0,s.Lk)("h2",null,"Top 5 des élèves",-1)),(0,s.Lk)("div",_,[(0,s.Lk)("table",I,[a[10]||(a[10]=(0,s.Lk)("thead",null,[(0,s.Lk)("tr",null,[(0,s.Lk)("th",null,"Rang"),(0,s.Lk)("th",null,"Élève"),(0,s.Lk)("th",null,"Classe"),(0,s.Lk)("th",null,"Moyenne")])],-1)),(0,s.Lk)("tbody",null,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(D.topEleves,((e,a)=>((0,s.uX)(),(0,s.CE)("tr",{key:a},[(0,s.Lk)("td",null,(0,t.v_)(a+1),1),(0,s.Lk)("td",null,(0,t.v_)(e.nom),1),(0,s.Lk)("td",null,(0,t.v_)(R.getClasseNom(e.classe)),1),(0,s.Lk)("td",null,(0,t.v_)(e.moyenne)+"/20",1)])))),128))])])])]),a[13]||(a[13]=(0,s.Fv)('<div class="card mt-4" data-v-963c08d0><h2 data-v-963c08d0>Répartition des moyennes</h2><div class="chart-container" data-v-963c08d0><div class="distribution-chart" data-v-963c08d0><div class="distribution-bar" data-v-963c08d0><div class="segment" style="width:5%;background-color:#f44336;" title="&lt; 8/20: 5%" data-v-963c08d0></div><div class="segment" style="width:15%;background-color:#ff9800;" title="8-10/20: 15%" data-v-963c08d0></div><div class="segment" style="width:40%;background-color:#ffc107;" title="10-12/20: 40%" data-v-963c08d0></div><div class="segment" style="width:25%;background-color:#8bc34a;" title="12-14/20: 25%" data-v-963c08d0></div><div class="segment" style="width:10%;background-color:#4caf50;" title="14-16/20: 10%" data-v-963c08d0></div><div class="segment" style="width:5%;background-color:#2196f3;" title="&gt; 16/20: 5%" data-v-963c08d0></div></div><div class="distribution-legend" data-v-963c08d0><div class="legend-item" data-v-963c08d0><div class="legend-color" style="background-color:#f44336;" data-v-963c08d0></div><div class="legend-label" data-v-963c08d0>Moins de 8/20: 5%</div></div><div class="legend-item" data-v-963c08d0><div class="legend-color" style="background-color:#ff9800;" data-v-963c08d0></div><div class="legend-label" data-v-963c08d0>8-10/20: 15%</div></div><div class="legend-item" data-v-963c08d0><div class="legend-color" style="background-color:#ffc107;" data-v-963c08d0></div><div class="legend-label" data-v-963c08d0>10-12/20: 40%</div></div><div class="legend-item" data-v-963c08d0><div class="legend-color" style="background-color:#8bc34a;" data-v-963c08d0></div><div class="legend-label" data-v-963c08d0>12-14/20: 25%</div></div><div class="legend-item" data-v-963c08d0><div class="legend-color" style="background-color:#4caf50;" data-v-963c08d0></div><div class="legend-label" data-v-963c08d0>14-16/20: 10%</div></div><div class="legend-item" data-v-963c08d0><div class="legend-color" style="background-color:#2196f3;" data-v-963c08d0></div><div class="legend-label" data-v-963c08d0>Plus de 16/20: 5%</div></div></div></div></div></div><div class="export-actions mt-4" data-v-963c08d0><button class="btn btn-secondary" data-v-963c08d0><i class="fas fa-file-excel" data-v-963c08d0></i> Export Excel </button><button class="btn btn-secondary" data-v-963c08d0><i class="fas fa-file-pdf" data-v-963c08d0></i> Export PDF </button></div>',2))])):(0,s.Q3)("",!0)])}d(8111),d(116);var D={name:"StatistiquesResultats",data(){return{selectedExamenId:"",selectedClasseId:"",statsGenerated:!1,examens:[{id:1,type:"Trimestre 1",dateDebut:"2024-09-15",dateFin:"2024-10-30",classesIds:[1,2],verrouille:!0},{id:2,type:"Trimestre 1",dateDebut:"2024-09-15",dateFin:"2024-10-30",classesIds:[3,4],verrouille:!1},{id:3,type:"Trimestre 2",dateDebut:"2024-12-01",dateFin:"2025-01-15",classesIds:[1,2,3,4],verrouille:!1}],classes:[{id:1,nom:"6ème A",effectif:35},{id:2,nom:"6ème B",effectif:32},{id:3,nom:"5ème A",effectif:30},{id:4,nom:"5ème B",effectif:28}],moyennesParMatiere:{1:{matiere:"Mathématiques",moyenne:12.75,tauxReussite:78},2:{matiere:"Physique",moyenne:11.3,tauxReussite:65},3:{matiere:"Français",moyenne:13.45,tauxReussite:82},4:{matiere:"Histoire-Géographie",moyenne:12.1,tauxReussite:70},5:{matiere:"Anglais",moyenne:14.2,tauxReussite:88}},topEleves:[{id:1,nom:"Dupont Jean",classe:1,moyenne:17.85},{id:2,nom:"Martin Sophie",classe:1,moyenne:16.92},{id:3,nom:"Dubois Pierre",classe:2,moyenne:16.45},{id:4,nom:"Lefebvre Marie",classe:3,moyenne:16.2},{id:5,nom:"Moreau Lucas",classe:2,moyenne:15.78}]}},computed:{canGenerateStats(){return this.selectedExamenId&&this.selectedClasseId}},methods:{generateStatistiques(){this.statsGenerated=!0,alert("Statistiques générées avec succès !")},formatDate(e){const a={day:"2-digit",month:"2-digit",year:"numeric"};return new Date(e).toLocaleDateString("fr-FR",a)},getClasseNom(e){const a=this.classes.find((a=>a.id===e));return a?a.nom:""}}},R=d(1241);const S=(0,R.A)(D,[["render",F],["__scopeId","data-v-963c08d0"]]);var X=S}}]);
//# sourceMappingURL=998.1cf200da.js.map