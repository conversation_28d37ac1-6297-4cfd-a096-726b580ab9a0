{"version": 3, "file": "js/998.1cf200da.js", "mappings": "oOAEOA,MAAM,0B,GAGJA,MAAM,Q,GAEJA,MAAM,gB,GACJA,MAAM,c,EARnB,U,GAkBaA,MAAM,c,EAlBnB,U,GA4BaA,MAAM,gB,EA5BnB,a,GAAAC,IAAA,EAoC+BD,MAAM,iB,GAC1BA,MAAM,a,GAEJA,MAAM,mB,GACJA,MAAM,a,GAEFA,MAAM,a,GAOdA,MAAM,a,GAEJA,MAAM,mB,GACJA,MAAM,a,GAEFA,MAAM,a,GA4CdA,MAAM,a,GAEJA,MAAM,oB,GACFA,MAAM,c,0CAnGrBE,EAAAA,EAAAA,IA2KM,MA3KNC,EA2KM,gBA1KJC,EAAAA,EAAAA,IAAmC,UAA/B,8BAA0B,KAE9BA,EAAAA,EAAAA,IA6BM,MA7BNC,EA6BM,cA5BJD,EAAAA,EAAAA,IAAgB,UAAZ,WAAO,KACXA,EAAAA,EAAAA,IA0BM,MA1BNE,EA0BM,EAzBJF,EAAAA,EAAAA,IAQM,MARNG,EAQM,cAPJH,EAAAA,EAAAA,IAAkC,SAA3BI,IAAI,UAAS,UAAM,cAC1BJ,EAAAA,EAAAA,IAKS,UALDK,GAAG,SAVrB,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GAUuCC,EAAAC,iBAAgBF,I,cAC3CP,EAAAA,EAAAA,IAAyD,UAAjDU,MAAM,GAAGC,SAAA,IAAS,0BAAsB,mBAChDb,EAAAA,EAAAA,IAESc,EAAAA,GAAA,MAdrBC,EAAAA,EAAAA,IAYqCL,EAAAM,SAAVC,K,WAAfjB,EAAAA,EAAAA,IAES,UAF0BD,IAAKkB,EAAOV,GAAKK,MAAOK,EAAOV,K,QAC7DU,EAAOC,MAAO,MAAEC,EAAAA,EAAAA,IAAGC,EAAAC,WAAWJ,EAAOK,YAAa,OAAGH,EAAAA,EAAAA,IAAGC,EAAAC,WAAWJ,EAAOM,UAAW,KAC1F,EAdZC,M,mBAUuCd,EAAAC,uBAQ/BT,EAAAA,EAAAA,IAQM,MARNuB,EAQM,cAPJvB,EAAAA,EAAAA,IAAkC,SAA3BI,IAAI,UAAS,UAAM,cAC1BJ,EAAAA,EAAAA,IAKS,UALDK,GAAG,SApBrB,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GAoBuCC,EAAAgB,iBAAgBjB,I,cAC3CP,EAAAA,EAAAA,IAA0D,UAAlDU,MAAM,GAAGC,SAAA,IAAS,2BAAuB,mBACjDb,EAAAA,EAAAA,IAESc,EAAAA,GAAA,MAxBrBC,EAAAA,EAAAA,IAsBqCL,EAAAiB,SAAVC,K,WAAf5B,EAAAA,EAAAA,IAES,UAF0BD,IAAK6B,EAAOrB,GAAKK,MAAOgB,EAAOrB,K,QAC7DqB,EAAOC,KAAG,EAvB3BC,M,mBAoBuCpB,EAAAgB,uBAQ/BxB,EAAAA,EAAAA,IAIM,MAJN6B,EAIM,EAHJ7B,EAAAA,EAAAA,IAES,UAFA8B,QAAKxB,EAAA,KAAAA,EAAA,OAAAyB,IAAEb,EAAAc,sBAAAd,EAAAc,wBAAAD,IAAsBnC,MAAM,kBAAmBe,UAAWO,EAAAe,kBAAkB,6BAE5F,EA/BVC,SAoCe1B,EAAA2B,iBAAc,WAAzBrC,EAAAA,EAAAA,IAwIM,MAxINsC,EAwIM,EAvIJpC,EAAAA,EAAAA,IAUM,MAVNqC,EAUM,cATJrC,EAAAA,EAAAA,IAA6B,UAAzB,wBAAoB,KACxBA,EAAAA,EAAAA,IAOM,MAPNsC,EAOM,EANJtC,EAAAA,EAAAA,IAKM,MALNuC,EAKM,gBAJJzC,EAAAA,EAAAA,IAGMc,EAAAA,GAAA,MA5ClBC,EAAAA,EAAAA,IAyCsCL,EAAAgC,oBAzCtC,CAyCyBC,EAAMpC,M,WAAnBP,EAAAA,EAAAA,IAGM,OAHyCD,IAAKQ,EAAIT,MAAM,Y,EAC5DI,EAAAA,EAAAA,IAA+C,MAA/C0C,GAA+CzB,EAAAA,EAAAA,IAArBwB,EAAKE,SAAO,IACtC3C,EAAAA,EAAAA,IAA6F,OAAxFJ,MAAM,YAAagD,OA3CtCC,EAAAA,EAAAA,IAAA,CAAAC,MA2CwE,EAAfL,EAAKM,QA3C9D,Q,QA2CmFN,EAAKM,SAAU,MAAG,Q,aAM/F/C,EAAAA,EAAAA,IAUM,MAVNgD,EAUM,cATJhD,EAAAA,EAAAA,IAAqC,UAAjC,gCAA4B,KAChCA,EAAAA,EAAAA,IAOM,MAPNiD,EAOM,EANJjD,EAAAA,EAAAA,IAKM,MALNkD,EAKM,gBAJJpD,EAAAA,EAAAA,IAGMc,EAAAA,GAAA,MAxDlBC,EAAAA,EAAAA,IAqDsCL,EAAAgC,oBArDtC,CAqDyBC,EAAMpC,M,WAAnBP,EAAAA,EAAAA,IAGM,OAHyCD,IAAKQ,EAAIT,MAAM,Y,EAC5DI,EAAAA,EAAAA,IAA+C,MAA/CmD,GAA+ClC,EAAAA,EAAAA,IAArBwB,EAAKE,SAAO,IACtC3C,EAAAA,EAAAA,IAAyG,OAApGJ,MAAM,oBAAqBgD,OAvD9CC,EAAAA,EAAAA,IAAA,CAAAC,MAAA,GAuDiEL,EAAKW,oB,QAAsBX,EAAKW,cAAe,IAAC,Q,2BAvDjHC,EAAAA,EAAAA,IAAA,qvCAkGMrD,EAAAA,EAAAA,IAsBM,MAtBNsD,EAsBM,gBArBJtD,EAAAA,EAAAA,IAAyB,UAArB,oBAAgB,KACpBA,EAAAA,EAAAA,IAmBM,MAnBNuD,EAmBM,EAlBJvD,EAAAA,EAAAA,IAiBQ,QAjBRwD,EAiBQ,gBAhBNxD,EAAAA,EAAAA,IAOQ,eANNA,EAAAA,EAAAA,IAKK,YAJHA,EAAAA,EAAAA,IAAa,UAAT,SACJA,EAAAA,EAAAA,IAAc,UAAV,UACJA,EAAAA,EAAAA,IAAe,UAAX,WACJA,EAAAA,EAAAA,IAAgB,UAAZ,eAAO,KAGfA,EAAAA,EAAAA,IAOQ,6BANNF,EAAAA,EAAAA,IAKKc,EAAAA,GAAA,MApHnBC,EAAAA,EAAAA,IA+G2CL,EAAAiD,WA/G3C,CA+G0BC,EAAOC,M,WAAnB7D,EAAAA,EAAAA,IAKK,MALoCD,IAAK8D,GAAK,EACjD3D,EAAAA,EAAAA,IAAwB,WAAAiB,EAAAA,EAAAA,IAAjB0C,EAAQ,GAAH,IACZ3D,EAAAA,EAAAA,IAAwB,WAAAiB,EAAAA,EAAAA,IAAjByC,EAAM/B,KAAG,IAChB3B,EAAAA,EAAAA,IAAyC,WAAAiB,EAAAA,EAAAA,IAAlCC,EAAA0C,aAAaF,EAAMhC,SAAM,IAChC1B,EAAAA,EAAAA,IAA+B,WAAAiB,EAAAA,EAAAA,IAAxByC,EAAMX,SAAU,MAAG,Q,6BAnH1CM,EAAAA,EAAAA,IAAA,22EAAAQ,EAAAA,EAAAA,IAAA,Q,oBAiLA,GACEC,KAAM,wBACNC,IAAAA,GACE,MAAO,CACLtD,iBAAkB,GAClBe,iBAAkB,GAClBW,gBAAgB,EAEhBrB,QAAS,CACP,CACET,GAAI,EACJW,KAAM,cACNI,UAAW,aACXC,QAAS,aACT2C,WAAY,CAAC,EAAG,GAChBC,YAAY,GAEd,CACE5D,GAAI,EACJW,KAAM,cACNI,UAAW,aACXC,QAAS,aACT2C,WAAY,CAAC,EAAG,GAChBC,YAAY,GAEd,CACE5D,GAAI,EACJW,KAAM,cACNI,UAAW,aACXC,QAAS,aACT2C,WAAY,CAAC,EAAG,EAAG,EAAG,GACtBC,YAAY,IAIhBxC,QAAS,CACP,CAAEpB,GAAI,EAAGsB,IAAK,SAAUuC,SAAU,IAClC,CAAE7D,GAAI,EAAGsB,IAAK,SAAUuC,SAAU,IAClC,CAAE7D,GAAI,EAAGsB,IAAK,SAAUuC,SAAU,IAClC,CAAE7D,GAAI,EAAGsB,IAAK,SAAUuC,SAAU,KAGpC1B,mBAAoB,CAClB,EAAG,CAAEG,QAAS,gBAAiBI,QAAS,MAAOK,aAAc,IAC7D,EAAG,CAAET,QAAS,WAAYI,QAAS,KAAOK,aAAc,IACxD,EAAG,CAAET,QAAS,WAAYI,QAAS,MAAOK,aAAc,IACxD,EAAG,CAAET,QAAS,sBAAuBI,QAAS,KAAOK,aAAc,IACnE,EAAG,CAAET,QAAS,UAAWI,QAAS,KAAOK,aAAc,KAGzDK,UAAW,CACT,CAAEpD,GAAI,EAAGsB,IAAK,cAAeD,OAAQ,EAAGqB,QAAS,OACjD,CAAE1C,GAAI,EAAGsB,IAAK,gBAAiBD,OAAQ,EAAGqB,QAAS,OACnD,CAAE1C,GAAI,EAAGsB,IAAK,gBAAiBD,OAAQ,EAAGqB,QAAS,OACnD,CAAE1C,GAAI,EAAGsB,IAAK,iBAAkBD,OAAQ,EAAGqB,QAAS,MACpD,CAAE1C,GAAI,EAAGsB,IAAK,eAAgBD,OAAQ,EAAGqB,QAAS,QAGxD,EACAoB,SAAU,CACRlC,gBAAAA,GACE,OAAOmC,KAAK3D,kBAAoB2D,KAAK5C,gBACvC,GAEF6C,QAAS,CACPrC,oBAAAA,GAGEoC,KAAKjC,gBAAiB,EAGtBmC,MAAM,sCACR,EAEAnD,UAAAA,CAAWoD,GACT,MAAMC,EAAU,CAAEC,IAAK,UAAWC,MAAO,UAAWC,KAAM,WAC1D,OAAO,IAAIC,KAAKL,GAAYM,mBAAmB,QAASL,EAC1D,EAEAZ,YAAAA,CAAakB,GACX,MAAMpD,EAAS0C,KAAK3C,QAAQsD,MAAKC,GAAKA,EAAE3E,KAAOyE,IAC/C,OAAOpD,EAASA,EAAOC,IAAM,EAC/B,I,UC5PJ,MAAMsD,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://projet_gestion_scolaire/./src/views/statistiques/StatistiquesResultats.vue", "webpack://projet_gestion_scolaire/./src/views/statistiques/StatistiquesResultats.vue?dd05"], "sourcesContent": ["/* eslint-disable */\n<template>\n  <div class=\"statistiques-container\">\n    <h1>Statistiques des Résultats</h1>\n    \n    <div class=\"card\">\n      <h2>Filtres</h2>\n      <div class=\"filters-form\">\n        <div class=\"form-group\">\n          <label for=\"examen\">Examen</label>\n          <select id=\"examen\" v-model=\"selectedExamenId\">\n            <option value=\"\" disabled>Sélectionner un examen</option>\n            <option v-for=\"examen in examens\" :key=\"examen.id\" :value=\"examen.id\">\n              {{ examen.type }} ({{ formatDate(examen.dateDebut) }} - {{ formatDate(examen.dateFin) }})\n            </option>\n          </select>\n        </div>\n        \n        <div class=\"form-group\">\n          <label for=\"classe\">Classe</label>\n          <select id=\"classe\" v-model=\"selectedClasseId\">\n            <option value=\"\" disabled>Sélectionner une classe</option>\n            <option v-for=\"classe in classes\" :key=\"classe.id\" :value=\"classe.id\">\n              {{ classe.nom }}\n            </option>\n          </select>\n        </div>\n        \n        <div class=\"form-actions\">\n          <button @click=\"generateStatistiques\" class=\"btn btn-primary\" :disabled=\"!canGenerateStats\">\n            Générer les statistiques\n          </button>\n        </div>\n      </div>\n    </div>\n    \n    <div v-if=\"statsGenerated\" class=\"stats-results\">\n      <div class=\"card mt-4\">\n        <h2>Moyennes par matière</h2>\n        <div class=\"chart-container\">\n          <div class=\"bar-chart\">\n            <div v-for=\"(stat, id) in moyennesParMatiere\" :key=\"id\" class=\"bar-item\">\n              <div class=\"bar-label\">{{ stat.matiere }}</div>\n              <div class=\"bar-value\" :style=\"{ width: `${stat.moyenne * 5}%` }\">{{ stat.moyenne }}/20</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"card mt-4\">\n        <h2>Taux de réussite par matière</h2>\n        <div class=\"chart-container\">\n          <div class=\"bar-chart\">\n            <div v-for=\"(stat, id) in moyennesParMatiere\" :key=\"id\" class=\"bar-item\">\n              <div class=\"bar-label\">{{ stat.matiere }}</div>\n              <div class=\"bar-value success\" :style=\"{ width: `${stat.tauxReussite}%` }\">{{ stat.tauxReussite }}%</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"card mt-4\">\n        <h2>Évolution des résultats</h2>\n        <div class=\"chart-container\">\n          <div class=\"line-chart-placeholder\">\n            <div class=\"line-chart\">\n              <div class=\"chart-legend\">\n                <div class=\"legend-item\">\n                  <div class=\"legend-color\" style=\"background-color: #3f51b5;\"></div>\n                  <div class=\"legend-label\">Moyenne générale</div>\n                </div>\n                <div class=\"legend-item\">\n                  <div class=\"legend-color\" style=\"background-color: #4caf50;\"></div>\n                  <div class=\"legend-label\">Taux de réussite</div>\n                </div>\n              </div>\n              <div class=\"chart-grid\">\n                <div class=\"chart-line\">\n                  <div class=\"point\"></div>\n                  <div class=\"point\"></div>\n                  <div class=\"point\"></div>\n                </div>\n                <div class=\"chart-line\">\n                  <div class=\"point\"></div>\n                  <div class=\"point\"></div>\n                  <div class=\"point\"></div>\n                </div>\n                <div class=\"x-axis\">\n                  <div class=\"tick\">Trimestre 1</div>\n                  <div class=\"tick\">Trimestre 2</div>\n                  <div class=\"tick\">Trimestre 3</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"card mt-4\">\n        <h2>Top 5 des élèves</h2>\n        <div class=\"table-responsive\">\n          <table class=\"data-table\">\n            <thead>\n              <tr>\n                <th>Rang</th>\n                <th>Élève</th>\n                <th>Classe</th>\n                <th>Moyenne</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr v-for=\"(eleve, index) in topEleves\" :key=\"index\">\n                <td>{{ index + 1 }}</td>\n                <td>{{ eleve.nom }}</td>\n                <td>{{ getClasseNom(eleve.classe) }}</td>\n                <td>{{ eleve.moyenne }}/20</td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n      \n      <div class=\"card mt-4\">\n        <h2>Répartition des moyennes</h2>\n        <div class=\"chart-container\">\n          <div class=\"distribution-chart\">\n            <div class=\"distribution-bar\">\n              <div class=\"segment\" style=\"width: 5%; background-color: #f44336;\" title=\"< 8/20: 5%\"></div>\n              <div class=\"segment\" style=\"width: 15%; background-color: #ff9800;\" title=\"8-10/20: 15%\"></div>\n              <div class=\"segment\" style=\"width: 40%; background-color: #ffc107;\" title=\"10-12/20: 40%\"></div>\n              <div class=\"segment\" style=\"width: 25%; background-color: #8bc34a;\" title=\"12-14/20: 25%\"></div>\n              <div class=\"segment\" style=\"width: 10%; background-color: #4caf50;\" title=\"14-16/20: 10%\"></div>\n              <div class=\"segment\" style=\"width: 5%; background-color: #2196f3;\" title=\"> 16/20: 5%\"></div>\n            </div>\n            <div class=\"distribution-legend\">\n              <div class=\"legend-item\">\n                <div class=\"legend-color\" style=\"background-color: #f44336;\"></div>\n                <div class=\"legend-label\">Moins de 8/20: 5%</div>\n              </div>\n              <div class=\"legend-item\">\n                <div class=\"legend-color\" style=\"background-color: #ff9800;\"></div>\n                <div class=\"legend-label\">8-10/20: 15%</div>\n              </div>\n              <div class=\"legend-item\">\n                <div class=\"legend-color\" style=\"background-color: #ffc107;\"></div>\n                <div class=\"legend-label\">10-12/20: 40%</div>\n              </div>\n              <div class=\"legend-item\">\n                <div class=\"legend-color\" style=\"background-color: #8bc34a;\"></div>\n                <div class=\"legend-label\">12-14/20: 25%</div>\n              </div>\n              <div class=\"legend-item\">\n                <div class=\"legend-color\" style=\"background-color: #4caf50;\"></div>\n                <div class=\"legend-label\">14-16/20: 10%</div>\n              </div>\n              <div class=\"legend-item\">\n                <div class=\"legend-color\" style=\"background-color: #2196f3;\"></div>\n                <div class=\"legend-label\">Plus de 16/20: 5%</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"export-actions mt-4\">\n        <button class=\"btn btn-secondary\">\n          <i class=\"fas fa-file-excel\"></i> Export Excel\n        </button>\n        <button class=\"btn btn-secondary\">\n          <i class=\"fas fa-file-pdf\"></i> Export PDF\n        </button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'StatistiquesResultats',\n  data() {\n    return {\n      selectedExamenId: '',\n      selectedClasseId: '',\n      statsGenerated: false,\n      \n      examens: [\n        { \n          id: 1, \n          type: 'Trimestre 1', \n          dateDebut: '2024-09-15', \n          dateFin: '2024-10-30', \n          classesIds: [1, 2], \n          verrouille: true \n        },\n        { \n          id: 2, \n          type: 'Trimestre 1', \n          dateDebut: '2024-09-15', \n          dateFin: '2024-10-30', \n          classesIds: [3, 4], \n          verrouille: false \n        },\n        { \n          id: 3, \n          type: 'Trimestre 2', \n          dateDebut: '2024-12-01', \n          dateFin: '2025-01-15', \n          classesIds: [1, 2, 3, 4], \n          verrouille: false \n        }\n      ],\n      \n      classes: [\n        { id: 1, nom: '6ème A', effectif: 35 },\n        { id: 2, nom: '6ème B', effectif: 32 },\n        { id: 3, nom: '5ème A', effectif: 30 },\n        { id: 4, nom: '5ème B', effectif: 28 }\n      ],\n      \n      moyennesParMatiere: {\n        1: { matiere: 'Mathématiques', moyenne: 12.75, tauxReussite: 78 },\n        2: { matiere: 'Physique', moyenne: 11.30, tauxReussite: 65 },\n        3: { matiere: 'Français', moyenne: 13.45, tauxReussite: 82 },\n        4: { matiere: 'Histoire-Géographie', moyenne: 12.10, tauxReussite: 70 },\n        5: { matiere: 'Anglais', moyenne: 14.20, tauxReussite: 88 }\n      },\n      \n      topEleves: [\n        { id: 1, nom: 'Dupont Jean', classe: 1, moyenne: 17.85 },\n        { id: 2, nom: 'Martin Sophie', classe: 1, moyenne: 16.92 },\n        { id: 3, nom: 'Dubois Pierre', classe: 2, moyenne: 16.45 },\n        { id: 4, nom: 'Lefebvre Marie', classe: 3, moyenne: 16.20 },\n        { id: 5, nom: 'Moreau Lucas', classe: 2, moyenne: 15.78 }\n      ]\n    }\n  },\n  computed: {\n    canGenerateStats() {\n      return this.selectedExamenId && this.selectedClasseId;\n    }\n  },\n  methods: {\n    generateStatistiques() {\n      // Dans une application réelle, on récupérerait les données depuis l'API\n      // Pour l'instant, on simule la génération\n      this.statsGenerated = true;\n      \n      // Afficher un message de succès\n      alert('Statistiques générées avec succès !');\n    },\n    \n    formatDate(dateString) {\n      const options = { day: '2-digit', month: '2-digit', year: 'numeric' };\n      return new Date(dateString).toLocaleDateString('fr-FR', options);\n    },\n    \n    getClasseNom(classeId) {\n      const classe = this.classes.find(c => c.id === classeId);\n      return classe ? classe.nom : '';\n    }\n  }\n}\n</script>\n\n<style scoped>\n.statistiques-container {\n  padding: 1.5rem;\n}\n\nh1 {\n  margin-bottom: 1.5rem;\n  color: #333;\n}\n\n.card {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\nh2 {\n  margin-top: 0;\n  margin-bottom: 1rem;\n  font-size: 1.2rem;\n  color: #333;\n  border-bottom: 1px solid #eee;\n  padding-bottom: 0.5rem;\n}\n\n.filters-form {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 1rem;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\nlabel {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #555;\n}\n\ninput, select {\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 1rem;\n}\n\n.form-actions {\n  grid-column: 1 / -1;\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  margin-top: 1rem;\n}\n\n.btn {\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: background-color 0.3s;\n  display: flex;\n  align-items: center;\n}\n\n.btn i {\n  margin-right: 0.5rem;\n}\n\n.btn-primary {\n  background-color: #3f51b5;\n  color: white;\n}\n\n.btn-primary:hover {\n  background-color: #303f9f;\n}\n\n.btn-secondary {\n  background-color: #f5f5f5;\n  color: #333;\n}\n\n.btn-secondary:hover {\n  background-color: #e0e0e0;\n}\n\n.btn:disabled {\n  background-color: #cccccc;\n  cursor: not-allowed;\n}\n\n.mt-4 {\n  margin-top: 1.5rem;\n}\n\n.chart-container {\n  margin-top: 1rem;\n  height: 300px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.bar-chart {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.bar-item {\n  display: flex;\n  align-items: center;\n}\n\n.bar-label {\n  width: 150px;\n  text-align: right;\n  padding-right: 1rem;\n  font-weight: 500;\n}\n\n.bar-value {\n  height: 30px;\n  background-color: #3f51b5;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  padding: 0 1rem;\n  border-radius: 4px;\n  font-weight: 500;\n}\n\n.bar-value.success {\n  background-color: #4caf50;\n}\n\n.line-chart-placeholder {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.line-chart {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.chart-legend {\n  display: flex;\n  justify-content: center;\n  gap: 2rem;\n  margin-bottom: 1rem;\n}\n\n.legend-item {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.legend-color {\n  width: 20px;\n  height: 20px;\n  border-radius: 4px;\n}\n\n.chart-grid {\n  position: relative;\n  flex-grow: 1;\n  border-left: 1px solid #ddd;\n  border-bottom: 1px solid #ddd;\n}\n\n.chart-line {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 60%;\n  border-top: 2px solid #3f51b5;\n}\n\n.chart-line:nth-child(2) {\n  height: 80%;\n  border-top-color: #4caf50;\n}\n\n.point {\n  position: absolute;\n  bottom: 100%;\n  left: 0%;\n  width: 8px;\n  height: 8px;\n  background-color: currentColor;\n  border-radius: 50%;\n  transform: translate(-50%, 50%);\n}\n\n.point:nth-child(2) {\n  left: 50%;\n}\n\n.point:nth-child(3) {\n  left: 100%;\n}\n\n.x-axis {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  display: flex;\n  justify-content: space-between;\n}\n\n.tick {\n  position: absolute;\n  bottom: -30px;\n  left: 0%;\n  transform: translateX(-50%);\n  text-align: center;\n  font-size: 0.9rem;\n  color: #757575;\n}\n\n.tick:nth-child(2) {\n  left: 50%;\n}\n\n.tick:nth-child(3) {\n  left: 100%;\n}\n\n.table-responsive {\n  overflow-x: auto;\n}\n\n.data-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.data-table th, .data-table td {\n  padding: 0.75rem;\n  text-align: left;\n  border-bottom: 1px solid #eee;\n}\n\n.data-table th {\n  background-color: #f5f5f5;\n  font-weight: 500;\n}\n\n.data-table tbody tr:hover {\n  background-color: #f9f9f9;\n}\n\n.distribution-chart {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.distribution-bar {\n  width: 100%;\n  height: 40px;\n  display: flex;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.segment {\n  height: 100%;\n}\n\n.distribution-legend {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  justify-content: center;\n}\n\n.export-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n}\n</style>\n", "import { render } from \"./StatistiquesResultats.vue?vue&type=template&id=963c08d0&scoped=true\"\nimport script from \"./StatistiquesResultats.vue?vue&type=script&lang=js\"\nexport * from \"./StatistiquesResultats.vue?vue&type=script&lang=js\"\n\nimport \"./StatistiquesResultats.vue?vue&type=style&index=0&id=963c08d0&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-963c08d0\"]])\n\nexport default __exports__"], "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "for", "id", "_cache", "$event", "$data", "selectedExamenId", "value", "disabled", "_Fragment", "_renderList", "examens", "examen", "type", "_toDisplayString", "$options", "formatDate", "dateDebut", "dateFin", "_hoisted_5", "_hoisted_6", "selectedClasseId", "classes", "classe", "nom", "_hoisted_7", "_hoisted_8", "onClick", "args", "generateStatistiques", "canGenerateStats", "_hoisted_9", "statsGenerated", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "moyennesParMatiere", "stat", "_hoisted_14", "matiere", "style", "_normalizeStyle", "width", "moyenne", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "tauxReussite", "_createStaticVNode", "_hoisted_19", "_hoisted_20", "_hoisted_21", "topEleves", "eleve", "index", "getClasseNom", "_createCommentVNode", "name", "data", "classesIds", "<PERSON><PERSON><PERSON><PERSON>", "effectif", "computed", "this", "methods", "alert", "dateString", "options", "day", "month", "year", "Date", "toLocaleDateString", "classeId", "find", "c", "__exports__", "render"], "sourceRoot": ""}