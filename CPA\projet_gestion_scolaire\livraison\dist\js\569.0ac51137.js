"use strict";(self["webpackChunkprojet_gestion_scolaire"]=self["webpackChunkprojet_gestion_scolaire"]||[]).push([[569],{1569:function(a,s,t){t.r(s),t.d(s,{default:function(){return k}});var c=t(6768);const i={class:"dashboard"},d={class:"dashboard-grid"},e={class:"dashboard-card finance"},l={class:"card-actions"},n={class:"dashboard-card academic"},v={class:"card-actions"},r={class:"dashboard-card bulletins"},f={class:"card-actions"},p={class:"dashboard-card stats"},o={class:"card-actions"};function u(a,s,t,u,b,m){const h=(0,c.g2)("router-link");return(0,c.uX)(),(0,c.CE)("div",i,[s[8]||(s[8]=(0,c.Lk)("h1",null,"Tableau de bord",-1)),(0,c.Lk)("div",d,[(0,c.Lk)("div",e,[s[1]||(s[1]=(0,c.Fv)('<h2 data-v-298cfa72>Finance</h2><div class="card-content" data-v-298cfa72><div class="stat-item" data-v-298cfa72><span class="stat-label" data-v-298cfa72>Encaissements du mois</span><span class="stat-value" data-v-298cfa72>1 250 000 Ar</span></div><div class="stat-item" data-v-298cfa72><span class="stat-label" data-v-298cfa72>Taux de paiement</span><span class="stat-value" data-v-298cfa72>78%</span></div></div>',2)),(0,c.Lk)("div",l,[(0,c.bF)(h,{to:"/finance/encaissements",class:"btn"},{default:(0,c.k6)((()=>s[0]||(s[0]=[(0,c.eW)("Voir détails")]))),_:1})])]),(0,c.Lk)("div",n,[s[3]||(s[3]=(0,c.Fv)('<h2 data-v-298cfa72>Académique</h2><div class="card-content" data-v-298cfa72><div class="stat-item" data-v-298cfa72><span class="stat-label" data-v-298cfa72>Examens en cours</span><span class="stat-value" data-v-298cfa72>2</span></div><div class="stat-item" data-v-298cfa72><span class="stat-label" data-v-298cfa72>Notes à saisir</span><span class="stat-value" data-v-298cfa72>3 classes</span></div></div>',2)),(0,c.Lk)("div",v,[(0,c.bF)(h,{to:"/notes",class:"btn"},{default:(0,c.k6)((()=>s[2]||(s[2]=[(0,c.eW)("Saisir notes")]))),_:1})])]),(0,c.Lk)("div",r,[s[5]||(s[5]=(0,c.Fv)('<h2 data-v-298cfa72>Bulletins</h2><div class="card-content" data-v-298cfa72><div class="stat-item" data-v-298cfa72><span class="stat-label" data-v-298cfa72>Bulletins générés</span><span class="stat-value" data-v-298cfa72>125</span></div><div class="stat-item" data-v-298cfa72><span class="stat-label" data-v-298cfa72>En attente</span><span class="stat-value" data-v-298cfa72>45</span></div></div>',2)),(0,c.Lk)("div",f,[(0,c.bF)(h,{to:"/bulletins",class:"btn"},{default:(0,c.k6)((()=>s[4]||(s[4]=[(0,c.eW)("Générer bulletins")]))),_:1})])]),(0,c.Lk)("div",p,[s[7]||(s[7]=(0,c.Fv)('<h2 data-v-298cfa72>Statistiques</h2><div class="card-content" data-v-298cfa72><div class="stat-item" data-v-298cfa72><span class="stat-label" data-v-298cfa72>Moyenne générale</span><span class="stat-value" data-v-298cfa72>12.8/20</span></div><div class="stat-item" data-v-298cfa72><span class="stat-label" data-v-298cfa72>Taux de réussite</span><span class="stat-value" data-v-298cfa72>65%</span></div></div>',2)),(0,c.Lk)("div",o,[(0,c.bF)(h,{to:"/statistiques",class:"btn"},{default:(0,c.k6)((()=>s[6]||(s[6]=[(0,c.eW)("Voir analyses")]))),_:1})])])]),s[9]||(s[9]=(0,c.Fv)('<div class="recent-activity" data-v-298cfa72><h2 data-v-298cfa72>Activités récentes</h2><ul class="activity-list" data-v-298cfa72><li class="activity-item" data-v-298cfa72><span class="activity-time" data-v-298cfa72>Aujourd&#39;hui, 10:45</span><span class="activity-desc" data-v-298cfa72>Paiement enregistré pour Rakoto Jean (6ème A)</span></li><li class="activity-item" data-v-298cfa72><span class="activity-time" data-v-298cfa72>Hier, 15:30</span><span class="activity-desc" data-v-298cfa72>Notes du Trimestre 1 validées pour la classe 5ème B</span></li><li class="activity-item" data-v-298cfa72><span class="activity-time" data-v-298cfa72>Hier, 09:15</span><span class="activity-desc" data-v-298cfa72>25 bulletins générés pour la classe 4ème A</span></li></ul></div>',1))])}var b={name:"DashboardView"},m=t(1241);const h=(0,m.A)(b,[["render",u],["__scopeId","data-v-298cfa72"]]);var k=h}}]);
//# sourceMappingURL=569.0ac51137.js.map