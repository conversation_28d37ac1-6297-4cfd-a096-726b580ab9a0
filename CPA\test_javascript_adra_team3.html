<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test JavaScript ADRA & TEAM 3</title>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <!-- Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h2>Test JavaScript ADRA & TEAM 3</h2>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="class_selector">Choisir une classe :</label>
                    <select id="class_selector" class="form-control select2">
                        <option value="">-- Sélectionner une classe --</option>
                        <option value="1">6e A</option>
                        <option value="2">5e B</option>
                        <option value="3">4e C</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="payment_selector">Paiements créés dans cette classe :</label>
                    <select id="payment_selector" class="form-control select2" disabled>
                        <option value="">-- Choisir d'abord une classe --</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info" id="debug-info">
            <h5>Informations de Debug :</h5>
            <div id="debug-content">Sélectionnez une classe pour tester...</div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>Console Logs</h5>
            </div>
            <div class="card-body">
                <div id="console-logs" style="background: #f8f9fa; padding: 10px; font-family: monospace; height: 300px; overflow-y: auto;">
                    <!-- Les logs apparaîtront ici -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Fonction pour afficher les logs dans la page
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('console-logs');
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            logElement.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // Vérification des dépendances
        if (typeof jQuery === 'undefined') {
            addLog('❌ jQuery is not loaded!', 'error');
        } else {
            addLog('✅ jQuery is loaded', 'success');
        }

        $(document).ready(function() {
            addLog('ADRA TEAM3 Script loaded', 'success');
            
            // Vérifier que tous les éléments existent
            if ($('#class_selector').length === 0) {
                addLog('❌ #class_selector not found!', 'error');
            } else {
                addLog('✅ #class_selector found', 'success');
            }
            
            if ($('#payment_selector').length === 0) {
                addLog('❌ #payment_selector not found!', 'error');
            } else {
                addLog('✅ #payment_selector found', 'success');
            }
            
            // Initialize Select2
            $('.select2').select2({
                placeholder: 'Sélectionner...',
                allowClear: true
            });
            addLog('✅ Select2 initialized', 'success');
            
            // Event handler for class selection
            $('#class_selector').on('change', function() {
                const classId = $(this).val();
                addLog('Class selector changed: ' + classId, 'info');
                loadClassPayments();
            });
            
            // Event handler for payment selection
            $('#payment_selector').on('change', function() {
                const paymentId = $(this).val();
                addLog('Payment selector changed: ' + paymentId, 'info');
                loadStudentsWithPayment();
            });
            
            // Test function availability
            if (typeof loadClassPayments === 'function') {
                addLog('✅ loadClassPayments function is available', 'success');
            } else {
                addLog('❌ loadClassPayments function is NOT available', 'error');
            }
        });

        // Load payments for selected class
        function loadClassPayments() {
            const classId = $('#class_selector').val();
            
            addLog('loadClassPayments called with classId: ' + classId, 'info');
            
            if (!classId) {
                $('#payment_selector').prop('disabled', true).html('<option value="">-- Choisir d\'abord une classe --</option>');
                $('#debug-content').html('Aucune classe sélectionnée');
                return;
            }
            
            // Show loading
            $('#payment_selector').prop('disabled', true).html('<option value="">Chargement des paiements...</option>');
            $('#debug-content').html('Chargement des paiements pour la classe ' + classId + '...');
            
            addLog('Loading payments for class: ' + classId, 'info');
            
            // Simuler une réponse AJAX pour le test
            setTimeout(function() {
                const mockPayments = [
                    { id: 1, title: 'Écolage Janvier', amount: 50000 },
                    { id: 2, title: 'Inscription', amount: 25000 },
                    { id: 3, title: 'Fournitures', amount: 15000 }
                ];
                
                let options = '<option value="">-- Sélectionner un paiement --</option>';
                mockPayments.forEach(function(payment) {
                    const formattedAmount = new Intl.NumberFormat('fr-FR').format(payment.amount) + ' Ar';
                    options += `<option value="${payment.id}" data-amount="${payment.amount}">${payment.title} (${formattedAmount})</option>`;
                });
                
                $('#payment_selector').prop('disabled', false).html(options);
                $('#debug-content').html(`Classe ${classId} sélectionnée. ${mockPayments.length} paiements trouvés.`);
                
                addLog('✅ Payments loaded successfully: ' + mockPayments.length + ' payments', 'success');
            }, 1000);
        }

        // Load students with selected payment
        function loadStudentsWithPayment() {
            const classId = $('#class_selector').val();
            const paymentId = $('#payment_selector').val();
            
            addLog('loadStudentsWithPayment called with classId: ' + classId + ', paymentId: ' + paymentId, 'info');
            
            if (!classId || !paymentId) {
                $('#debug-content').html('Classe et paiement requis');
                return;
            }
            
            const selectedPayment = $('#payment_selector option:selected');
            const paymentName = selectedPayment.text();
            const paymentAmount = parseInt(selectedPayment.data('amount'));
            
            $('#debug-content').html(`Paiement sélectionné: ${paymentName} - ${paymentAmount} Ar`);
            
            addLog('✅ Payment selected: ' + paymentName + ' (' + paymentAmount + ' Ar)', 'success');
        }

        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('fr-FR').format(amount) + ' Ar';
        }
    </script>
</body>
</html>
