/* 
 * Dashboard Interactif
 * Styles pour les éléments interactifs du tableau de bord
 */

/* Éléments modifiables */
.stat-editable, .status-editable {
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.stat-editable:hover, .status-editable:hover {
    color: #2196F3;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.stat-editable::after, .status-editable::after {
    content: '✏️';
    font-size: 0.7em;
    position: absolute;
    top: -5px;
    right: -15px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-editable:hover::after, .status-editable:hover::after {
    opacity: 1;
}

/* Cartes et widgets */
.dashboard-card {
    transition: all 0.3s ease;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.dashboard-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-5px);
}

/* Statistiques */
.stat-value, .kpi-value, .metric-value {
    transition: all 0.3s ease;
    font-weight: bold;
}

.counter-animated {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Badges de statut */
.badge {
    transition: all 0.3s ease;
    padding: 5px 10px;
    border-radius: 20px;
}

.badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

/* Tableaux */
.table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    margin-bottom: 1rem;
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 15px;
    font-weight: 600;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: translateX(5px);
}

.table tbody td {
    padding: 10px 15px;
    transition: all 0.3s ease;
}

/* Boîte de dialogue d'édition */
.edit-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.edit-dialog-content {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    width: 400px;
    max-width: 90%;
    animation: dialogFadeIn 0.3s ease;
}

@keyframes dialogFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.edit-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.edit-dialog-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.edit-dialog-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
}

.edit-dialog-body {
    margin-bottom: 20px;
}

.edit-dialog-footer {
    display: flex;
    justify-content: flex-end;
}

.edit-dialog-footer button {
    margin-left: 10px;
}

/* Notifications */
.update-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    padding: 15px;
    display: flex;
    align-items: center;
    z-index: 9999;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 300px;
}

.notification-icon {
    margin-right: 15px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.notification-success .notification-icon {
    background: #28a745;
}

.notification-warning .notification-icon {
    background: #ffc107;
}

.notification-error .notification-icon {
    background: #dc3545;
}

.notification-info .notification-icon {
    background: #17a2b8;
}

.notification-content p {
    margin: 0;
    font-size: 14px;
}

/* Animations */
@keyframes value-update {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
}

@keyframes section-fade-out {
    0% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-10px); }
}

@keyframes section-fade-in {
    0% { opacity: 0; transform: translateY(10px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* Cartes de statistiques rapides */
.quick-stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.quick-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 2.5rem;
    margin-right: 20px;
    color: #667eea;
    opacity: 0.8;
}

.stat-content {
    flex: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
}

/* Couleurs pour les cartes */
.bg-primary-50 {
    background-color: rgba(102, 126, 234, 0.1);
}

.bg-success-50 {
    background-color: rgba(40, 167, 69, 0.1);
}

.bg-danger-50 {
    background-color: rgba(220, 53, 69, 0.1);
}

.bg-warning-50 {
    background-color: rgba(255, 193, 7, 0.1);
}

.bg-info-50 {
    background-color: rgba(23, 162, 184, 0.1);
}

/* Indicateurs KPI */
.kpi-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.kpi-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.kpi-content {
    position: relative;
    z-index: 1;
}

.kpi-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.kpi-label {
    font-size: 0.9rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.kpi-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 3rem;
    opacity: 0.2;
}

.kpi-trend {
    margin-top: 10px;
    font-size: 0.8rem;
    color: #6c757d;
}

/* Graphiques */
.chart-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.chart-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
}

.chart-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
    display: flex;
    align-items: center;
}

.chart-title i {
    margin-right: 10px;
    color: #667eea;
}

.chart-body {
    padding: 20px;
}

/* Responsive design */
@media (max-width: 768px) {
    .quick-stat-card, .kpi-card, .chart-card {
        margin-bottom: 15px;
    }
    
    .quick-stat-card:hover, .kpi-card:hover, .chart-card:hover {
        transform: translateY(-3px);
    }
    
    .kpi-value {
        font-size: 1.5rem;
    }
    
    .stat-icon, .kpi-icon {
        font-size: 2rem;
    }
}