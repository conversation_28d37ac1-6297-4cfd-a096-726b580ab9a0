{"version": 3, "file": "js/286.7f82cf19.js", "mappings": "qOACOA,MAAM,sB,GAGJA,MAAM,Q,GAEJA,MAAM,gB,GACJA,MAAM,c,GAWNA,MAAM,c,GAKNA,MAAM,c,GAKNA,MAAM,gB,GA5BnBC,IAAA,EAoCkCD,MAAM,oB,GAC7BA,MAAM,a,GAEJA,MAAM,iB,GACJA,MAAM,uB,GAIJA,MAAM,gB,GAENA,MAAM,U,GAIRA,MAAM,wB,GAIJA,MAAM,gB,GAENA,MAAM,U,GAIRA,MAAM,wB,GAIJA,MAAM,gB,GA8DZA,MAAM,a,GAEJA,MAAM,oB,GACFA,MAAM,c,0CAhIrBE,EAAAA,EAAAA,IA6KM,MA7KNC,EA6KM,gBA5KJC,EAAAA,EAAAA,IAA4B,UAAxB,uBAAmB,KAEvBA,EAAAA,EAAAA,IA8BM,MA9BNC,EA8BM,cA7BJD,EAAAA,EAAAA,IAAgB,UAAZ,WAAO,KACXA,EAAAA,EAAAA,IA2BM,MA3BNE,EA2BM,EA1BJF,EAAAA,EAAAA,IASM,MATNG,EASM,cARJH,EAAAA,EAAAA,IAAoC,SAA7BI,IAAI,WAAU,WAAO,cAC5BJ,EAAAA,EAAAA,IAMS,UANDK,GAAG,UATrB,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GASwCC,EAAAC,gBAAeF,I,cATvDG,EAAAA,EAAAA,IAAA,iTASwCF,EAAAC,sBAShCT,EAAAA,EAAAA,IAGM,MAHNW,EAGM,cAFJX,EAAAA,EAAAA,IAAyC,SAAlCI,IAAI,aAAY,cAAU,cACjCJ,EAAAA,EAAAA,IAAsD,SAA/CY,KAAK,OAAOP,GAAG,YApBhC,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GAoBqDC,EAAAK,UAASN,I,iBAATC,EAAAK,gBAG7Cb,EAAAA,EAAAA,IAGM,MAHNc,EAGM,cAFJd,EAAAA,EAAAA,IAAqC,SAA9BI,IAAI,WAAU,YAAQ,cAC7BJ,EAAAA,EAAAA,IAAkD,SAA3CY,KAAK,OAAOP,GAAG,UAzBhC,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GAyBmDC,EAAAO,QAAOR,I,iBAAPC,EAAAO,cAG3Cf,EAAAA,EAAAA,IAIM,MAJNgB,EAIM,EAHJhB,EAAAA,EAAAA,IAES,UAFAiB,QAAKX,EAAA,KAAAA,EAAA,OAAAY,IAAEC,EAAAC,kBAAAD,EAAAC,oBAAAF,IAAkBtB,MAAM,mBAAkB,+BAOrDY,EAAAa,oBAAiB,WAA5BvB,EAAAA,EAAAA,IAyIM,MAzINwB,EAyIM,EAxIJtB,EAAAA,EAAAA,IAmCM,MAnCNuB,EAmCM,gBAlCJvB,EAAAA,EAAAA,IAA0C,UAAtC,qCAAiC,KACrCA,EAAAA,EAAAA,IAgCM,MAhCNwB,EAgCM,EA/BJxB,EAAAA,EAAAA,IAQM,MARNyB,EAQM,gBAPJzB,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,aAAW,EACpBI,EAAAA,EAAAA,IAAsC,KAAnCJ,MAAM,6BAAwB,KAEnCI,EAAAA,EAAAA,IAGM,MAHN0B,EAGM,cAFJ1B,EAAAA,EAAAA,IAA4B,UAAxB,uBAAmB,KACvBA,EAAAA,EAAAA,IAAyE,IAAzE2B,GAAyEC,EAAAA,EAAAA,IAApDT,EAAAU,cAAcrB,EAAAsB,SAASC,qBAAsB,MAAG,QAIzE/B,EAAAA,EAAAA,IAQM,MARNgC,EAQM,gBAPJhC,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,aAAW,EACpBI,EAAAA,EAAAA,IAAmC,KAAhCJ,MAAM,0BAAqB,KAEhCI,EAAAA,EAAAA,IAGM,MAHNiC,EAGM,gBAFJjC,EAAAA,EAAAA,IAA4B,UAAxB,uBAAmB,KACvBA,EAAAA,EAAAA,IAAyE,IAAzEkC,GAAyEN,EAAAA,EAAAA,IAApDT,EAAAU,cAAcrB,EAAAsB,SAASK,qBAAsB,MAAG,QAIzEnC,EAAAA,EAAAA,IAUM,MAVNoC,EAUM,gBATJpC,EAAAA,EAAAA,IAEM,OAFDJ,MAAM,aAAW,EACpBI,EAAAA,EAAAA,IAAoC,KAAjCJ,MAAM,2BAAsB,KAEjCI,EAAAA,EAAAA,IAKM,MALNqC,EAKM,gBAJJrC,EAAAA,EAAAA,IAAc,UAAV,SAAK,KACTA,EAAAA,EAAAA,IAEI,KAFDJ,OAlEjB0C,EAAAA,EAAAA,IAAA,CAkEuB,SAAQ,UAAuB9B,EAAAsB,SAASS,OAAS,EAAJ,SAAmB/B,EAAAsB,SAASS,MAAQ,O,QACrFpB,EAAAU,cAAcrB,EAAAsB,SAASS,QAAS,OACrC,0BApEd7B,EAAAA,EAAAA,IAAA,uzDA8HMV,EAAAA,EAAAA,IAqCM,MArCNwC,EAqCM,gBApCJxC,EAAAA,EAAAA,IAA8B,UAA1B,yBAAqB,KACzBA,EAAAA,EAAAA,IAkCM,MAlCNyC,EAkCM,EAjCJzC,EAAAA,EAAAA,IAgCQ,QAhCR0C,EAgCQ,gBA/BN1C,EAAAA,EAAAA,IAMQ,eALNA,EAAAA,EAAAA,IAIK,YAHHA,EAAAA,EAAAA,IAAc,UAAV,UACJA,EAAAA,EAAAA,IAAgB,UAAZ,YACJA,EAAAA,EAAAA,IAAoB,UAAhB,mBAAW,KAGnBA,EAAAA,EAAAA,IAgBQ,eAfNA,EAAAA,EAAAA,IAIK,0BAHHA,EAAAA,EAAAA,IAA2B,UAAvB,sBAAkB,KACtBA,EAAAA,EAAAA,IAAuC,WAAA4B,EAAAA,EAAAA,IAAhCT,EAAAU,cAAc,OAAU,MAAG,kBAClC7B,EAAAA,EAAAA,IAAY,UAAR,OAAG,OAETA,EAAAA,EAAAA,IAIK,0BAHHA,EAAAA,EAAAA,IAAuB,UAAnB,kBAAc,KAClBA,EAAAA,EAAAA,IAAuC,WAAA4B,EAAAA,EAAAA,IAAhCT,EAAAU,cAAc,OAAU,MAAG,kBAClC7B,EAAAA,EAAAA,IAAY,UAAR,OAAG,OAETA,EAAAA,EAAAA,IAIK,0BAHHA,EAAAA,EAAAA,IAA4B,UAAxB,uBAAmB,KACvBA,EAAAA,EAAAA,IAAuC,WAAA4B,EAAAA,EAAAA,IAAhCT,EAAAU,cAAc,MAAU,MAAG,kBAClC7B,EAAAA,EAAAA,IAAY,UAAR,OAAG,SAGXA,EAAAA,EAAAA,IAMQ,eALNA,EAAAA,EAAAA,IAIK,0BAHHA,EAAAA,EAAAA,IAAc,UAAV,SAAK,KACTA,EAAAA,EAAAA,IAAwC,WAAA4B,EAAAA,EAAAA,IAAjCT,EAAAU,cAAc,MAAW,MAAG,kBACnC7B,EAAAA,EAAAA,IAAa,UAAT,QAAI,6BA9JxBU,EAAAA,EAAAA,IAAA,kTAAAiC,EAAAA,EAAAA,IAAA,Q,CAkLA,OACEC,KAAM,kBACNC,IAAAA,GACE,MAAO,CACLpC,gBAAiB,OACjBI,UAAW,GACXE,QAAS,GACTM,mBAAmB,EACnBS,SAAU,CACRC,mBAAoB,IACpBI,mBAAoB,KACpBI,MAAO,KACPO,iBAAkB,CAChBC,KAAM,KACNC,KAAM,MAERC,kBAAmB,CACjB,qBAAsB,KACtB,iBAAmB,KACnB,sBAAwB,KAE1BC,QAAS,QAGf,EACAC,QAAS,CACP/B,gBAAAA,GAGEgC,KAAK/B,mBAAoB,EAGzBgC,MAAM,iCACR,EAEAxB,aAAAA,CAAcyB,GACZ,OAAO,IAAIC,KAAKC,aAAa,SAASC,OAAOH,EAC/C,I,UChNJ,MAAMI,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://projet_gestion_scolaire/./src/views/finance/Synthese.vue", "webpack://projet_gestion_scolaire/./src/views/finance/Synthese.vue?e14e"], "sourcesContent": ["<template>\n  <div class=\"synthese-container\">\n    <h1>Synthèse Financière</h1>\n    \n    <div class=\"card\">\n      <h2>Filtres</h2>\n      <div class=\"filters-form\">\n        <div class=\"form-group\">\n          <label for=\"periode\">Période</label>\n          <select id=\"periode\" v-model=\"selectedPeriode\">\n            <option value=\"jour\">Journalier</option>\n            <option value=\"semaine\">Hebdomadaire</option>\n            <option value=\"mois\">Mensuel</option>\n            <option value=\"trimestre\">Trimestriel</option>\n            <option value=\"annee\">Annuel</option>\n          </select>\n        </div>\n        \n        <div class=\"form-group\">\n          <label for=\"dateDebut\">Date début</label>\n          <input type=\"date\" id=\"dateDebut\" v-model=\"dateDebut\">\n        </div>\n        \n        <div class=\"form-group\">\n          <label for=\"dateFin\">Date fin</label>\n          <input type=\"date\" id=\"dateFin\" v-model=\"dateFin\">\n        </div>\n        \n        <div class=\"form-actions\">\n          <button @click=\"generateSynthese\" class=\"btn btn-primary\">\n            Générer la synthèse\n          </button>\n        </div>\n      </div>\n    </div>\n    \n    <div v-if=\"syntheseGenerated\" class=\"synthese-results\">\n      <div class=\"card mt-4\">\n        <h2>Résumé des opérations financières</h2>\n        <div class=\"summary-cards\">\n          <div class=\"summary-card income\">\n            <div class=\"card-icon\">\n              <i class=\"fas fa-money-bill-wave\"></i>\n            </div>\n            <div class=\"card-content\">\n              <h3>Total Encaissements</h3>\n              <p class=\"amount\">{{ formatMontant(synthese.totalEncaissements) }} Ar</p>\n            </div>\n          </div>\n          \n          <div class=\"summary-card expense\">\n            <div class=\"card-icon\">\n              <i class=\"fas fa-file-invoice\"></i>\n            </div>\n            <div class=\"card-content\">\n              <h3>Total Décaissements</h3>\n              <p class=\"amount\">{{ formatMontant(synthese.totalDecaissements) }} Ar</p>\n            </div>\n          </div>\n          \n          <div class=\"summary-card balance\">\n            <div class=\"card-icon\">\n              <i class=\"fas fa-balance-scale\"></i>\n            </div>\n            <div class=\"card-content\">\n              <h3>Solde</h3>\n              <p class=\"amount\" :class=\"{ 'positive': synthese.solde >= 0, 'negative': synthese.solde < 0 }\">\n                {{ formatMontant(synthese.solde) }} Ar\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"card mt-4\">\n        <h2>Taux de paiement par classe</h2>\n        <div class=\"chart-container\">\n          <div class=\"chart-placeholder\">\n            <div class=\"bar-chart\">\n              <div class=\"bar-item\">\n                <div class=\"bar-label\">6ème A</div>\n                <div class=\"bar-value\" style=\"width: 85%;\">85%</div>\n              </div>\n              <div class=\"bar-item\">\n                <div class=\"bar-label\">6ème B</div>\n                <div class=\"bar-value\" style=\"width: 78%;\">78%</div>\n              </div>\n              <div class=\"bar-item\">\n                <div class=\"bar-label\">5ème A</div>\n                <div class=\"bar-value\" style=\"width: 92%;\">92%</div>\n              </div>\n              <div class=\"bar-item\">\n                <div class=\"bar-label\">5ème B</div>\n                <div class=\"bar-value\" style=\"width: 65%;\">65%</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"card mt-4\">\n        <h2>Répartition par mode de paiement</h2>\n        <div class=\"chart-container\">\n          <div class=\"pie-chart-container\">\n            <div class=\"pie-chart\">\n              <div class=\"pie-slice\" style=\"--percentage: 65; --color: #3f51b5;\">\n                <span class=\"pie-label\">Cash</span>\n              </div>\n              <div class=\"pie-slice\" style=\"--percentage: 35; --color: #f44336;\">\n                <span class=\"pie-label\">ADRA</span>\n              </div>\n            </div>\n            <div class=\"pie-legend\">\n              <div class=\"legend-item\">\n                <div class=\"legend-color\" style=\"background-color: #3f51b5;\"></div>\n                <div class=\"legend-label\">Cash: 65%</div>\n              </div>\n              <div class=\"legend-item\">\n                <div class=\"legend-color\" style=\"background-color: #f44336;\"></div>\n                <div class=\"legend-label\">ADRA: 35%</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"card mt-4\">\n        <h2>Répartition par motif</h2>\n        <div class=\"table-responsive\">\n          <table class=\"data-table\">\n            <thead>\n              <tr>\n                <th>Motif</th>\n                <th>Montant</th>\n                <th>Pourcentage</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr>\n                <td>Frais de scolarité</td>\n                <td>{{ formatMontant(750000) }} Ar</td>\n                <td>75%</td>\n              </tr>\n              <tr>\n                <td>Frais d'examen</td>\n                <td>{{ formatMontant(150000) }} Ar</td>\n                <td>15%</td>\n              </tr>\n              <tr>\n                <td>Frais d'inscription</td>\n                <td>{{ formatMontant(100000) }} Ar</td>\n                <td>10%</td>\n              </tr>\n            </tbody>\n            <tfoot>\n              <tr>\n                <th>Total</th>\n                <th>{{ formatMontant(1000000) }} Ar</th>\n                <th>100%</th>\n              </tr>\n            </tfoot>\n          </table>\n        </div>\n      </div>\n      \n      <div class=\"export-actions mt-4\">\n        <button class=\"btn btn-secondary\">\n          <i class=\"fas fa-file-excel\"></i> Export Excel\n        </button>\n        <button class=\"btn btn-secondary\">\n          <i class=\"fas fa-file-pdf\"></i> Export PDF\n        </button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FinanceSynthese',\n  data() {\n    return {\n      selectedPeriode: 'mois',\n      dateDebut: '',\n      dateFin: '',\n      syntheseGenerated: false,\n      synthese: {\n        totalEncaissements: 1000000,\n        totalDecaissements: 470000,\n        solde: 530000,\n        repartitionModes: {\n          Cash: 650000,\n          ADRA: 350000\n        },\n        repartitionMotifs: {\n          'Frais de scolarité': 750000,\n          'Frais d\\'examen': 150000,\n          'Frais d\\'inscription': 100000\n        },\n        periode: 'mois'\n      }\n    }\n  },\n  methods: {\n    generateSynthese() {\n      // Dans une application réelle, on récupérerait les données depuis l'API\n      // Pour l'instant, on simule la génération\n      this.syntheseGenerated = true;\n      \n      // Afficher un message de succès\n      alert('Synthèse générée avec succès !');\n    },\n    \n    formatMontant(montant) {\n      return new Intl.NumberFormat('fr-FR').format(montant);\n    }\n  }\n}\n</script>\n\n<style scoped>\n.synthese-container {\n  padding: 1.5rem;\n}\n\nh1 {\n  margin-bottom: 1.5rem;\n  color: #333;\n}\n\n.card {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\nh2 {\n  margin-top: 0;\n  margin-bottom: 1rem;\n  font-size: 1.2rem;\n  color: #333;\n  border-bottom: 1px solid #eee;\n  padding-bottom: 0.5rem;\n}\n\n.filters-form {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 1rem;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\nlabel {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #555;\n}\n\ninput, select {\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 1rem;\n}\n\n.form-actions {\n  grid-column: 1 / -1;\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  margin-top: 1rem;\n}\n\n.btn {\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: background-color 0.3s;\n  display: flex;\n  align-items: center;\n}\n\n.btn i {\n  margin-right: 0.5rem;\n}\n\n.btn-primary {\n  background-color: #3f51b5;\n  color: white;\n}\n\n.btn-primary:hover {\n  background-color: #303f9f;\n}\n\n.btn-secondary {\n  background-color: #f5f5f5;\n  color: #333;\n}\n\n.btn-secondary:hover {\n  background-color: #e0e0e0;\n}\n\n.mt-4 {\n  margin-top: 1.5rem;\n}\n\n.summary-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 1.5rem;\n  margin-top: 1rem;\n}\n\n.summary-card {\n  display: flex;\n  align-items: center;\n  padding: 1.5rem;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.summary-card.income {\n  background-color: #e8f5e9;\n  border-left: 4px solid #4caf50;\n}\n\n.summary-card.expense {\n  background-color: #fff3e0;\n  border-left: 4px solid #ff9800;\n}\n\n.summary-card.balance {\n  background-color: #e3f2fd;\n  border-left: 4px solid #2196f3;\n}\n\n.card-icon {\n  font-size: 2rem;\n  margin-right: 1.5rem;\n  color: #757575;\n}\n\n.card-content h3 {\n  margin: 0 0 0.5rem 0;\n  font-size: 1rem;\n  color: #757575;\n}\n\n.amount {\n  font-size: 1.5rem;\n  font-weight: bold;\n  margin: 0;\n  color: #333;\n}\n\n.amount.positive {\n  color: #4caf50;\n}\n\n.amount.negative {\n  color: #f44336;\n}\n\n.chart-container {\n  margin-top: 1rem;\n  height: 300px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.chart-placeholder {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.bar-chart {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.bar-item {\n  display: flex;\n  align-items: center;\n}\n\n.bar-label {\n  width: 100px;\n  text-align: right;\n  padding-right: 1rem;\n  font-weight: 500;\n}\n\n.bar-value {\n  height: 30px;\n  background-color: #3f51b5;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  padding: 0 1rem;\n  border-radius: 4px;\n  font-weight: 500;\n}\n\n.pie-chart-container {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  width: 100%;\n}\n\n.pie-chart {\n  position: relative;\n  width: 200px;\n  height: 200px;\n  border-radius: 50%;\n  background-color: #f5f5f5;\n  overflow: hidden;\n}\n\n.pie-slice {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 50% 100%);\n  transform: rotate(calc(var(--percentage) * 3.6deg));\n  transform-origin: center;\n  background-color: var(--color);\n}\n\n.pie-label {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-weight: bold;\n}\n\n.pie-legend {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.legend-item {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.legend-color {\n  width: 20px;\n  height: 20px;\n  border-radius: 4px;\n}\n\n.table-responsive {\n  overflow-x: auto;\n}\n\n.data-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.data-table th, .data-table td {\n  padding: 0.75rem;\n  text-align: left;\n  border-bottom: 1px solid #eee;\n}\n\n.data-table th {\n  background-color: #f5f5f5;\n  font-weight: 500;\n}\n\n.data-table tfoot th {\n  background-color: #e0e0e0;\n}\n\n.export-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n}\n</style>\n", "import { render } from \"./Synthese.vue?vue&type=template&id=4710345f&scoped=true\"\nimport script from \"./Synthese.vue?vue&type=script&lang=js\"\nexport * from \"./Synthese.vue?vue&type=script&lang=js\"\n\nimport \"./Synthese.vue?vue&type=style&index=0&id=4710345f&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-4710345f\"]])\n\nexport default __exports__"], "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "for", "id", "_cache", "$event", "$data", "selectedPeriode", "_createStaticVNode", "_hoisted_5", "type", "dateDebut", "_hoisted_6", "dateFin", "_hoisted_7", "onClick", "args", "$options", "generateSynthese", "syntheseGenerated", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_toDisplayString", "formatMontant", "synthese", "totalEncaissements", "_hoisted_14", "_hoisted_15", "_hoisted_16", "totalDecaissements", "_hoisted_17", "_hoisted_18", "_normalizeClass", "solde", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_createCommentVNode", "name", "data", "repartitionModes", "Cash", "ADRA", "repartitionMotifs", "periode", "methods", "this", "alert", "montant", "Intl", "NumberFormat", "format", "__exports__", "render"], "sourceRoot": ""}