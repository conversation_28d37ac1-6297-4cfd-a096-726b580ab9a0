# Tutoriels - Système de Gestion Scolaire

Ce document contient une série de tutoriels pour vous aider à maîtriser les fonctionnalités principales du système de gestion scolaire.

## Tutoriel 1 : Enregistrer un encaissement

Ce tutoriel vous guidera pas à pas pour enregistrer un nouvel encaissement dans le système.

### Étape 1 : Accéder au module Finance
- Connectez-vous à l'application
- Dans le menu latéral, cliquez sur "Finance"
- Sélectionnez "Encaissements"

### Étape 2 : Créer un nouvel encaissement
- Cliquez sur le bouton "Nouvel encaissement" en haut à droite
- Un formulaire s'affiche pour saisir les informations

### Étape 3 : Remplir le formulaire
- Date : Sélectionnez la date de l'encaissement
- Élève : Sélectionnez l'élève concerné dans la liste déroulante
- Montant : Entrez le montant encaissé (en Ariary)
- Motif : Sélection<PERSON>z le motif (<PERSON><PERSON> de scolarité, Frais d'examen, etc.)
- Mode de paiement : Sélectionnez "Cash" ou "ADRA"

### Étape 4 : Enregistrer et imprimer
- Cliquez sur "Enregistrer"
- Un message de confirmation s'affiche
- Pour imprimer un reçu, cliquez sur l'icône d'impression à côté de l'encaissement

## Tutoriel 2 : Saisir les notes d'une classe

Ce tutoriel vous guidera pour saisir les notes d'une classe pour un examen.

### Étape 1 : Accéder au module Notes
- Connectez-vous à l'application
- Dans le menu latéral, cliquez sur "Notes"
- Sélectionnez "Saisie des notes"

### Étape 2 : Sélectionner l'examen et la classe
- Dans le formulaire de filtrage :
  - Sélectionnez l'examen (ex: "Trimestre 1")
  - Sélectionnez la classe (ex: "6ème A")
- Cliquez sur "Afficher"

### Étape 3 : Saisir les notes
- Un tableau s'affiche avec tous les élèves en lignes et toutes les matières en colonnes
- Pour chaque élève et chaque matière, saisissez :
  - DS1 : Note du premier devoir surveillé
  - DS2 : Note du deuxième devoir surveillé
  - Examen : Note de l'examen final
- Les moyennes sont calculées automatiquement

### Étape 4 : Enregistrer les notes
- Après avoir saisi toutes les notes, cliquez sur "Enregistrer"
- Un message de confirmation s'affiche
- Les notes sont maintenant enregistrées et peuvent être utilisées pour générer les bulletins

## Tutoriel 3 : Générer les bulletins d'une classe

Ce tutoriel vous guidera pour générer les bulletins scolaires d'une classe entière.

### Étape 1 : Accéder au module Bulletins
- Connectez-vous à l'application
- Dans le menu latéral, cliquez sur "Bulletins"
- Sélectionnez "Génération"

### Étape 2 : Configurer les paramètres
- Sélectionnez l'examen (ex: "Trimestre 1")
- Sélectionnez la classe (ex: "6ème A")
- Laissez le champ "Élève" vide pour générer les bulletins de toute la classe
- Format : Sélectionnez "A4"
- Options :
  - Cochez "Inclure signature du directeur"
  - Cochez "Inclure graphique de progression"
  - Cochez "Inclure appréciation générale"

### Étape 3 : Générer et télécharger
- Cliquez sur "Générer PDF"
- Patientez pendant la génération (cela peut prendre quelques secondes)
- Une fois terminé, cliquez sur "Télécharger tous" pour obtenir un fichier ZIP contenant tous les bulletins
- Alternativement, vous pouvez télécharger ou imprimer les bulletins individuellement

## Tutoriel 4 : Consulter les statistiques de résultats

Ce tutoriel vous guidera pour analyser les statistiques de résultats d'une classe.

### Étape 1 : Accéder au module Statistiques
- Connectez-vous à l'application
- Dans le menu latéral, cliquez sur "Statistiques"
- Sélectionnez "Résultats"

### Étape 2 : Sélectionner les paramètres
- Sélectionnez l'examen (ex: "Trimestre 1")
- Sélectionnez la classe (ex: "6ème A")
- Cliquez sur "Générer les statistiques"

### Étape 3 : Explorer les statistiques
- Consultez les différentes sections :
  - "Moyennes par matière" : graphique en barres des moyennes pour chaque matière
  - "Taux de réussite par matière" : pourcentage d'élèves ayant obtenu la moyenne dans chaque matière
  - "Évolution des résultats" : comparaison avec les trimestres précédents
  - "Top 5 des élèves" : tableau des 5 meilleurs élèves de la classe
  - "Répartition des moyennes" : distribution des moyennes par tranches

### Étape 4 : Exporter les données
- Pour exporter les statistiques, cliquez sur :
  - "Export Excel" pour obtenir un fichier Excel
  - "Export PDF" pour obtenir un document PDF

## Tutoriel 5 : Configurer un nouvel examen

Ce tutoriel vous guidera pour configurer un nouvel examen dans le système.

### Étape 1 : Accéder au module Examens
- Connectez-vous à l'application
- Dans le menu latéral, cliquez sur "Examens"
- Sélectionnez "Liste des examens"

### Étape 2 : Créer un nouvel examen
- Cliquez sur le bouton "Nouvel examen" en haut à droite
- Un formulaire s'affiche pour saisir les informations

### Étape 3 : Remplir le formulaire
- Type : Entrez le type d'examen (ex: "Trimestre 2")
- Date de début : Sélectionnez la date de début de l'examen
- Date de fin : Sélectionnez la date de fin de l'examen
- Classes concernées : Cochez les classes qui participeront à cet examen

### Étape 4 : Enregistrer
- Cliquez sur "Enregistrer"
- Un message de confirmation s'affiche
- Le nouvel examen apparaît dans la liste des examens

## Conseils pour une utilisation optimale

### Optimisation du flux de travail
- Configurez tous les examens en début d'année scolaire
- Utilisez les filtres pour retrouver rapidement les informations
- Sauvegardez régulièrement lors de la saisie des notes

### Raccourcis clavier utiles
- Tab : passer au champ suivant dans un formulaire
- Shift+Tab : revenir au champ précédent
- Entrée : valider un formulaire
- Ctrl+S : sauvegarder (dans certains écrans)

### Résolution des problèmes courants
- Si les moyennes ne se calculent pas correctement, vérifiez que toutes les notes sont saisies
- Si un élève n'apparaît pas dans une liste, vérifiez qu'il est bien assigné à la classe sélectionnée
- Si vous ne pouvez pas modifier des notes, vérifiez que l'examen n'est pas verrouillé
