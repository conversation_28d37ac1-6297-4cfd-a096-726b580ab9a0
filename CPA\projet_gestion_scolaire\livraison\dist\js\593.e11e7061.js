"use strict";(self["webpackChunkprojet_gestion_scolaire"]=self["webpackChunkprojet_gestion_scolaire"]||[]).push([[593],{593:function(e,t,a){a.r(t),a.d(t,{default:function(){return _}});var i=a(6768),n=a(5130),s=a(4232);const l={class:"decaissement-container"},r={class:"card"},c={class:"form-group"},o={class:"form-group"},u={class:"form-group"},d={class:"form-group"},f={class:"form-group"},m={class:"card mt-4"},b={class:"search-form"},k={class:"form-group"},p={class:"form-group"},L={class:"form-group"},h={class:"form-actions"},v={class:"table-responsive mt-3"},D={class:"data-table"},w=["onClick"],y={key:1},g={key:0};function F(e,t,a,F,j,C){return(0,i.uX)(),(0,i.CE)("div",l,[t[27]||(t[27]=(0,i.Lk)("h1",null,"Gestion des Décaissements",-1)),(0,i.Lk)("div",r,[t[17]||(t[17]=(0,i.Lk)("h2",null,"Création d'un ordre de paiement",-1)),(0,i.Lk)("form",{onSubmit:t[5]||(t[5]=(0,n.D$)(((...e)=>C.saveDecaissement&&C.saveDecaissement(...e)),["prevent"])),class:"decaissement-form"},[(0,i.Lk)("div",c,[t[10]||(t[10]=(0,i.Lk)("label",{for:"date"},"Date",-1)),(0,i.bo)((0,i.Lk)("input",{type:"date",id:"date","onUpdate:modelValue":t[0]||(t[0]=e=>j.newDecaissement.date=e),required:""},null,512),[[n.Jo,j.newDecaissement.date]])]),(0,i.Lk)("div",o,[t[11]||(t[11]=(0,i.Lk)("label",{for:"beneficiaire"},"Bénéficiaire",-1)),(0,i.bo)((0,i.Lk)("input",{type:"text",id:"beneficiaire","onUpdate:modelValue":t[1]||(t[1]=e=>j.newDecaissement.beneficiaire=e),placeholder:"Nom du bénéficiaire",required:""},null,512),[[n.Jo,j.newDecaissement.beneficiaire]])]),(0,i.Lk)("div",u,[t[12]||(t[12]=(0,i.Lk)("label",{for:"montant"},"Montant (Ar)",-1)),(0,i.bo)((0,i.Lk)("input",{type:"number",id:"montant","onUpdate:modelValue":t[2]||(t[2]=e=>j.newDecaissement.montant=e),min:"0",required:""},null,512),[[n.Jo,j.newDecaissement.montant]])]),(0,i.Lk)("div",d,[t[13]||(t[13]=(0,i.Lk)("label",{for:"motif"},"Motif",-1)),(0,i.bo)((0,i.Lk)("textarea",{id:"motif","onUpdate:modelValue":t[3]||(t[3]=e=>j.newDecaissement.motif=e),rows:"3",required:""},null,512),[[n.Jo,j.newDecaissement.motif]])]),(0,i.Lk)("div",f,[t[14]||(t[14]=(0,i.Lk)("label",{for:"justificatif"},"Justificatif (optionnel)",-1)),(0,i.Lk)("input",{type:"file",id:"justificatif",onChange:t[4]||(t[4]=(...e)=>C.handleFileUpload&&C.handleFileUpload(...e))},null,32),t[15]||(t[15]=(0,i.Lk)("small",null,"Formats acceptés: PDF, JPG, PNG (max 5MB)",-1))]),t[16]||(t[16]=(0,i.Lk)("div",{class:"form-actions"},[(0,i.Lk)("button",{type:"reset",class:"btn btn-secondary"},"Annuler"),(0,i.Lk)("button",{type:"submit",class:"btn btn-primary"},"Enregistrer")],-1))],32)]),(0,i.Lk)("div",m,[t[25]||(t[25]=(0,i.Lk)("h2",null,"Liste des décaissements",-1)),(0,i.Lk)("div",b,[(0,i.Lk)("div",k,[t[18]||(t[18]=(0,i.Lk)("label",{for:"searchBeneficiaire"},"Recherche par bénéficiaire",-1)),(0,i.bo)((0,i.Lk)("input",{type:"text",id:"searchBeneficiaire","onUpdate:modelValue":t[6]||(t[6]=e=>j.searchBeneficiaire=e),placeholder:"Nom du bénéficiaire"},null,512),[[n.Jo,j.searchBeneficiaire]])]),(0,i.Lk)("div",p,[t[19]||(t[19]=(0,i.Lk)("label",{for:"dateDebut"},"Date début",-1)),(0,i.bo)((0,i.Lk)("input",{type:"date",id:"dateDebut","onUpdate:modelValue":t[7]||(t[7]=e=>j.dateDebut=e)},null,512),[[n.Jo,j.dateDebut]])]),(0,i.Lk)("div",L,[t[20]||(t[20]=(0,i.Lk)("label",{for:"dateFin"},"Date fin",-1)),(0,i.bo)((0,i.Lk)("input",{type:"date",id:"dateFin","onUpdate:modelValue":t[8]||(t[8]=e=>j.dateFin=e)},null,512),[[n.Jo,j.dateFin]])]),(0,i.Lk)("div",h,[(0,i.Lk)("button",{onClick:t[9]||(t[9]=(...e)=>C.searchDecaissements&&C.searchDecaissements(...e)),class:"btn btn-primary"},"Rechercher")])]),(0,i.Lk)("div",v,[(0,i.Lk)("table",D,[t[24]||(t[24]=(0,i.Lk)("thead",null,[(0,i.Lk)("tr",null,[(0,i.Lk)("th",null,"Date"),(0,i.Lk)("th",null,"Bénéficiaire"),(0,i.Lk)("th",null,"Montant (Ar)"),(0,i.Lk)("th",null,"Motif"),(0,i.Lk)("th",null,"Justificatif"),(0,i.Lk)("th",null,"Actions")])],-1)),(0,i.Lk)("tbody",null,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(j.decaissements,((e,a)=>((0,i.uX)(),(0,i.CE)("tr",{key:a},[(0,i.Lk)("td",null,(0,s.v_)(C.formatDate(e.date)),1),(0,i.Lk)("td",null,(0,s.v_)(e.beneficiaire),1),(0,i.Lk)("td",null,(0,s.v_)(C.formatMontant(e.montant)),1),(0,i.Lk)("td",null,(0,s.v_)(e.motif),1),(0,i.Lk)("td",null,[e.justificatif?((0,i.uX)(),(0,i.CE)("a",{key:0,href:"#",onClick:(0,n.D$)((t=>C.viewJustificatif(e)),["prevent"])},t[21]||(t[21]=[(0,i.Lk)("i",{class:"fas fa-file-pdf"},null,-1),(0,i.eW)(" Voir ")]),8,w)):((0,i.uX)(),(0,i.CE)("span",y,"-"))]),t[22]||(t[22]=(0,i.Lk)("td",null,[(0,i.Lk)("button",{class:"btn-icon",title:"Imprimer ordre"},[(0,i.Lk)("i",{class:"fas fa-print"})]),(0,i.Lk)("button",{class:"btn-icon",title:"Voir détails"},[(0,i.Lk)("i",{class:"fas fa-eye"})])],-1))])))),128)),0===j.decaissements.length?((0,i.uX)(),(0,i.CE)("tr",g,t[23]||(t[23]=[(0,i.Lk)("td",{colspan:"6",class:"text-center"},"Aucun décaissement trouvé",-1)]))):(0,i.Q3)("",!0)])])]),t[26]||(t[26]=(0,i.Fv)('<div class="export-actions mt-3" data-v-ae92b29e><button class="btn btn-secondary" data-v-ae92b29e><i class="fas fa-file-excel" data-v-ae92b29e></i> Export Excel </button><button class="btn btn-secondary" data-v-ae92b29e><i class="fas fa-file-pdf" data-v-ae92b29e></i> Export PDF </button></div>',1))])])}var j={name:"FinanceDecaissement",data(){return{newDecaissement:{date:(new Date).toISOString().substr(0,10),beneficiaire:"",montant:"",motif:"",justificatif:null},decaissements:[{id:1,date:"2024-03-10",beneficiaire:"Fournisseur Papeterie",montant:12e4,motif:"Achat fournitures",justificatif:"facture_001.pdf"},{id:2,date:"2024-03-18",beneficiaire:"JIRAMA",montant:35e4,motif:"Facture électricité",justificatif:"facture_jirama.pdf"}],searchBeneficiaire:"",dateDebut:"",dateFin:""}},methods:{saveDecaissement(){const e={id:Date.now(),...this.newDecaissement};this.decaissements.unshift(e),this.newDecaissement={date:(new Date).toISOString().substr(0,10),beneficiaire:"",montant:"",motif:"",justificatif:null},alert("Décaissement enregistré avec succès !")},handleFileUpload(e){const t=e.target.files[0];t&&(this.newDecaissement.justificatif=t.name)},searchDecaissements(){alert("Recherche effectuée !")},viewJustificatif(e){alert(`Affichage du justificatif: ${e.justificatif}`)},formatDate(e){const t={day:"2-digit",month:"2-digit",year:"numeric"};return new Date(e).toLocaleDateString("fr-FR",t)},formatMontant(e){return new Intl.NumberFormat("fr-FR").format(e)}}},C=a(1241);const J=(0,C.A)(j,[["render",F],["__scopeId","data-v-ae92b29e"]]);var _=J}}]);
//# sourceMappingURL=593.e11e7061.js.map