"use strict";(self["webpackChunkprojet_gestion_scolaire"]=self["webpackChunkprojet_gestion_scolaire"]||[]).push([[716],{4716:function(e,l,t){t.r(l),t.d(l,{default:function(){return _}});var a=t(6768),n=t(5130),s=t(4232);const o={class:"examens-container"},i={class:"card"},r={class:"form-group"},u={class:"form-group"},d={class:"form-group"},c={class:"form-group full-width"},m={class:"checkbox-group"},k=["id","value"],v=["for"],p={class:"form-group"},b={class:"toggle-switch"},L={class:"toggle-label"},f={class:"card mt-4"},x={class:"table-responsive"},g={class:"data-table"},E=["onClick","title"],h=["onClick"],y={key:0};function D(e,l,t,D,w,C){const F=(0,a.g2)("router-link");return(0,a.uX)(),(0,a.CE)("div",o,[l[20]||(l[20]=(0,a.Lk)("h1",null,"Gestion des Examens",-1)),(0,a.Lk)("div",i,[l[14]||(l[14]=(0,a.Lk)("h2",null,"Paramétrage d'un examen",-1)),(0,a.Lk)("form",{onSubmit:l[5]||(l[5]=(0,n.D$)(((...e)=>C.saveExamen&&C.saveExamen(...e)),["prevent"])),class:"examen-form"},[(0,a.Lk)("div",r,[l[7]||(l[7]=(0,a.Lk)("label",{for:"type"},"Type d'examen",-1)),(0,a.bo)((0,a.Lk)("select",{id:"type","onUpdate:modelValue":l[0]||(l[0]=e=>w.newExamen.type=e),required:""},l[6]||(l[6]=[(0,a.Fv)('<option value="" disabled data-v-72c50e9b>Sélectionner un type</option><option value="Trimestre 1" data-v-72c50e9b>Trimestre 1</option><option value="Trimestre 2" data-v-72c50e9b>Trimestre 2</option><option value="Trimestre 3" data-v-72c50e9b>Trimestre 3</option><option value="Semestre 1" data-v-72c50e9b>Semestre 1</option><option value="Semestre 2" data-v-72c50e9b>Semestre 2</option><option value="Examen Final" data-v-72c50e9b>Examen Final</option>',7)]),512),[[n.u1,w.newExamen.type]])]),(0,a.Lk)("div",u,[l[8]||(l[8]=(0,a.Lk)("label",{for:"dateDebut"},"Date de début",-1)),(0,a.bo)((0,a.Lk)("input",{type:"date",id:"dateDebut","onUpdate:modelValue":l[1]||(l[1]=e=>w.newExamen.dateDebut=e),required:""},null,512),[[n.Jo,w.newExamen.dateDebut]])]),(0,a.Lk)("div",d,[l[9]||(l[9]=(0,a.Lk)("label",{for:"dateFin"},"Date de fin",-1)),(0,a.bo)((0,a.Lk)("input",{type:"date",id:"dateFin","onUpdate:modelValue":l[2]||(l[2]=e=>w.newExamen.dateFin=e),required:""},null,512),[[n.Jo,w.newExamen.dateFin]])]),(0,a.Lk)("div",c,[l[10]||(l[10]=(0,a.Lk)("label",null,"Classes concernées",-1)),(0,a.Lk)("div",m,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(w.classes,(e=>((0,a.uX)(),(0,a.CE)("div",{key:e.id,class:"checkbox-item"},[(0,a.bo)((0,a.Lk)("input",{type:"checkbox",id:"classe-"+e.id,value:e.id,"onUpdate:modelValue":l[3]||(l[3]=e=>w.newExamen.classesIds=e)},null,8,k),[[n.lH,w.newExamen.classesIds]]),(0,a.Lk)("label",{for:"classe-"+e.id},(0,s.v_)(e.nom),9,v)])))),128))])]),(0,a.Lk)("div",p,[l[12]||(l[12]=(0,a.Lk)("label",{for:"verrouillage"},"Verrouillage des notes",-1)),(0,a.Lk)("div",b,[(0,a.bo)((0,a.Lk)("input",{type:"checkbox",id:"verrouillage","onUpdate:modelValue":l[4]||(l[4]=e=>w.newExamen.verrouille=e)},null,512),[[n.lH,w.newExamen.verrouille]]),l[11]||(l[11]=(0,a.Lk)("label",{for:"verrouillage"},null,-1)),(0,a.Lk)("span",L,(0,s.v_)(w.newExamen.verrouille?"Verrouillé":"Déverrouillé"),1)])]),l[13]||(l[13]=(0,a.Lk)("div",{class:"form-actions"},[(0,a.Lk)("button",{type:"reset",class:"btn btn-secondary"},"Annuler"),(0,a.Lk)("button",{type:"submit",class:"btn btn-primary"},"Enregistrer")],-1))],32)]),(0,a.Lk)("div",f,[l[19]||(l[19]=(0,a.Lk)("h2",null,"Liste des examens",-1)),(0,a.Lk)("div",x,[(0,a.Lk)("table",g,[l[18]||(l[18]=(0,a.Lk)("thead",null,[(0,a.Lk)("tr",null,[(0,a.Lk)("th",null,"Type"),(0,a.Lk)("th",null,"Période"),(0,a.Lk)("th",null,"Classes"),(0,a.Lk)("th",null,"Statut"),(0,a.Lk)("th",null,"Actions")])],-1)),(0,a.Lk)("tbody",null,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(w.examens,((e,t)=>((0,a.uX)(),(0,a.CE)("tr",{key:t},[(0,a.Lk)("td",null,(0,s.v_)(e.type),1),(0,a.Lk)("td",null,(0,s.v_)(C.formatDate(e.dateDebut))+" - "+(0,s.v_)(C.formatDate(e.dateFin)),1),(0,a.Lk)("td",null,(0,s.v_)(C.getClassesNames(e.classesIds)),1),(0,a.Lk)("td",null,[(0,a.Lk)("span",{class:(0,s.C4)(["status-badge",e.verrouille?"locked":"unlocked"])},(0,s.v_)(e.verrouille?"Verrouillé":"Déverrouillé"),3)]),(0,a.Lk)("td",null,[(0,a.Lk)("button",{onClick:l=>C.toggleVerrouillage(e),class:"btn-icon",title:e.verrouille?"Déverrouiller":"Verrouiller"},[(0,a.Lk)("i",{class:(0,s.C4)(["fas",e.verrouille?"fa-lock":"fa-lock-open"])},null,2)],8,E),(0,a.Lk)("button",{onClick:l=>C.editExamen(e),class:"btn-icon",title:"Modifier"},l[15]||(l[15]=[(0,a.Lk)("i",{class:"fas fa-edit"},null,-1)]),8,h),(0,a.bF)(F,{to:"/notes?examenId="+e.id,class:"btn-icon",title:"Saisir les notes"},{default:(0,a.k6)((()=>l[16]||(l[16]=[(0,a.Lk)("i",{class:"fas fa-pen"},null,-1)]))),_:2},1032,["to"])])])))),128)),0===w.examens.length?((0,a.uX)(),(0,a.CE)("tr",y,l[17]||(l[17]=[(0,a.Lk)("td",{colspan:"5",class:"text-center"},"Aucun examen trouvé",-1)]))):(0,a.Q3)("",!0)])])])])])}t(4114),t(8111),t(116),t(1701);var w={name:"ExamensList",data(){return{newExamen:{type:"",dateDebut:"",dateFin:"",classesIds:[],verrouille:!1},examens:[{id:1,type:"Trimestre 1",dateDebut:"2024-09-15",dateFin:"2024-10-30",classesIds:[1,2],verrouille:!0},{id:2,type:"Trimestre 1",dateDebut:"2024-09-15",dateFin:"2024-10-30",classesIds:[3,4],verrouille:!1}],classes:[{id:1,nom:"6ème A",effectif:35},{id:2,nom:"6ème B",effectif:32},{id:3,nom:"5ème A",effectif:30},{id:4,nom:"5ème B",effectif:28}]}},methods:{saveExamen(){const e={id:Date.now(),...this.newExamen};this.examens.push(e),this.newExamen={type:"",dateDebut:"",dateFin:"",classesIds:[],verrouille:!1},alert("Examen créé avec succès !")},editExamen(){alert("Fonctionnalité d'édition à implémenter")},toggleVerrouillage(e){e.verrouille=!e.verrouille,alert(`Examen ${e.verrouille?"verrouillé":"déverrouillé"} avec succès !`)},formatDate(e){const l={day:"2-digit",month:"2-digit",year:"numeric"};return new Date(e).toLocaleDateString("fr-FR",l)},getClassesNames(e){return e.map((e=>{const l=this.classes.find((l=>l.id===e));return l?l.nom:""})).join(", ")}}},C=t(1241);const F=(0,C.A)(w,[["render",D],["__scopeId","data-v-72c50e9b"]]);var _=F}}]);
//# sourceMappingURL=716.51576e7c.js.map