"use strict";(self["webpackChunkprojet_gestion_scolaire"]=self["webpackChunkprojet_gestion_scolaire"]||[]).push([[936],{4936:function(e,t,i){i.r(t),i.d(t,{default:function(){return E}});var a=i(6768),n=i(5130),r=i(4232);const o={class:"matieres-container"},l={class:"card"},d={class:"form-group"},s={class:"form-group"},c={class:"form-group"},u={class:"card mt-4"},m={class:"table-responsive"},f={class:"data-table"},k=["onClick"],b=["onClick"],p={key:0},L={key:0,class:"modal-overlay"},v={class:"modal-content"},M={class:"form-group"},h={class:"form-group"},y={class:"form-group"},C={class:"form-actions"};function x(e,t,i,x,g,w){return(0,a.uX)(),(0,a.CE)("div",o,[t[24]||(t[24]=(0,a.Lk)("h1",null,"Gestion des Matières",-1)),(0,a.Lk)("div",l,[t[13]||(t[13]=(0,a.Lk)("h2",null,"Création d'une matière",-1)),(0,a.Lk)("form",{onSubmit:t[3]||(t[3]=(0,n.D$)(((...e)=>w.saveMatiere&&w.saveMatiere(...e)),["prevent"])),class:"matiere-form"},[(0,a.Lk)("div",d,[t[9]||(t[9]=(0,a.Lk)("label",{for:"nom"},"Nom complet",-1)),(0,a.bo)((0,a.Lk)("input",{type:"text",id:"nom","onUpdate:modelValue":t[0]||(t[0]=e=>g.newMatiere.nom=e),placeholder:"Ex: Mathématiques",required:""},null,512),[[n.Jo,g.newMatiere.nom]])]),(0,a.Lk)("div",s,[t[10]||(t[10]=(0,a.Lk)("label",{for:"abreviation"},"Abréviation",-1)),(0,a.bo)((0,a.Lk)("input",{type:"text",id:"abreviation","onUpdate:modelValue":t[1]||(t[1]=e=>g.newMatiere.abreviation=e),placeholder:"Ex: MATH",required:"",maxlength:"5"},null,512),[[n.Jo,g.newMatiere.abreviation]])]),(0,a.Lk)("div",c,[t[11]||(t[11]=(0,a.Lk)("label",{for:"coefficient"},"Coefficient",-1)),(0,a.bo)((0,a.Lk)("input",{type:"number",id:"coefficient","onUpdate:modelValue":t[2]||(t[2]=e=>g.newMatiere.coefficient=e),min:"1",max:"10",required:""},null,512),[[n.Jo,g.newMatiere.coefficient]])]),t[12]||(t[12]=(0,a.Lk)("div",{class:"form-actions"},[(0,a.Lk)("button",{type:"reset",class:"btn btn-secondary"},"Annuler"),(0,a.Lk)("button",{type:"submit",class:"btn btn-primary"},"Enregistrer")],-1))],32)]),(0,a.Lk)("div",u,[t[18]||(t[18]=(0,a.Lk)("h2",null,"Liste des matières",-1)),(0,a.Lk)("div",m,[(0,a.Lk)("table",f,[t[17]||(t[17]=(0,a.Lk)("thead",null,[(0,a.Lk)("tr",null,[(0,a.Lk)("th",null,"Nom"),(0,a.Lk)("th",null,"Abréviation"),(0,a.Lk)("th",null,"Coefficient"),(0,a.Lk)("th",null,"Actions")])],-1)),(0,a.Lk)("tbody",null,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(w.matieresSorted,((e,i)=>((0,a.uX)(),(0,a.CE)("tr",{key:i},[(0,a.Lk)("td",null,(0,r.v_)(e.nom),1),(0,a.Lk)("td",null,(0,r.v_)(e.abreviation),1),(0,a.Lk)("td",null,(0,r.v_)(e.coefficient),1),(0,a.Lk)("td",null,[(0,a.Lk)("button",{onClick:t=>w.editMatiere(e),class:"btn-icon",title:"Modifier"},t[14]||(t[14]=[(0,a.Lk)("i",{class:"fas fa-edit"},null,-1)]),8,k),(0,a.Lk)("button",{onClick:t=>w.confirmDeleteMatiere(e.id),class:"btn-icon",title:"Supprimer"},t[15]||(t[15]=[(0,a.Lk)("i",{class:"fas fa-trash"},null,-1)]),8,b)])])))),128)),0===g.matieres.length?((0,a.uX)(),(0,a.CE)("tr",p,t[16]||(t[16]=[(0,a.Lk)("td",{colspan:"4",class:"text-center"},"Aucune matière trouvée",-1)]))):(0,a.Q3)("",!0)])])])]),g.editMode?((0,a.uX)(),(0,a.CE)("div",L,[(0,a.Lk)("div",v,[t[23]||(t[23]=(0,a.Lk)("h3",null,"Modifier une matière",-1)),(0,a.Lk)("form",{onSubmit:t[8]||(t[8]=(0,n.D$)(((...e)=>w.updateMatiere&&w.updateMatiere(...e)),["prevent"])),class:"matiere-form"},[(0,a.Lk)("div",M,[t[19]||(t[19]=(0,a.Lk)("label",{for:"editNom"},"Nom complet",-1)),(0,a.bo)((0,a.Lk)("input",{type:"text",id:"editNom","onUpdate:modelValue":t[4]||(t[4]=e=>g.editedMatiere.nom=e),required:""},null,512),[[n.Jo,g.editedMatiere.nom]])]),(0,a.Lk)("div",h,[t[20]||(t[20]=(0,a.Lk)("label",{for:"editAbreviation"},"Abréviation",-1)),(0,a.bo)((0,a.Lk)("input",{type:"text",id:"editAbreviation","onUpdate:modelValue":t[5]||(t[5]=e=>g.editedMatiere.abreviation=e),required:"",maxlength:"5"},null,512),[[n.Jo,g.editedMatiere.abreviation]])]),(0,a.Lk)("div",y,[t[21]||(t[21]=(0,a.Lk)("label",{for:"editCoefficient"},"Coefficient",-1)),(0,a.bo)((0,a.Lk)("input",{type:"number",id:"editCoefficient","onUpdate:modelValue":t[6]||(t[6]=e=>g.editedMatiere.coefficient=e),min:"1",max:"10",required:""},null,512),[[n.Jo,g.editedMatiere.coefficient]])]),(0,a.Lk)("div",C,[(0,a.Lk)("button",{type:"button",onClick:t[7]||(t[7]=(...e)=>w.cancelEdit&&w.cancelEdit(...e)),class:"btn btn-secondary"},"Annuler"),t[22]||(t[22]=(0,a.Lk)("button",{type:"submit",class:"btn btn-primary"},"Mettre à jour",-1))])],32)])])):(0,a.Q3)("",!0)])}i(4114),i(8111),i(2489);var g={name:"MatieresList",data(){return{newMatiere:{nom:"",abreviation:"",coefficient:1},matieres:[{id:1,nom:"Mathématiques",abreviation:"MATH",coefficient:4},{id:2,nom:"Physique",abreviation:"PHY",coefficient:3},{id:3,nom:"Français",abreviation:"FR",coefficient:3},{id:4,nom:"Histoire-Géographie",abreviation:"HIST",coefficient:2}],editMode:!1,editedMatiere:null,editedIndex:-1}},computed:{matieresSorted(){return[...this.matieres].sort(((e,t)=>e.nom.localeCompare(t.nom)))}},methods:{saveMatiere(){const e={id:Date.now(),...this.newMatiere};this.matieres.push(e),this.newMatiere={nom:"",abreviation:"",coefficient:1},alert("Matière ajoutée avec succès !")},editMatiere(e){this.editMode=!0,this.editedIndex=this.matieres.findIndex((t=>t.id===e.id)),this.editedMatiere={...e}},updateMatiere(){-1!==this.editedIndex&&this.matieres.splice(this.editedIndex,1,this.editedMatiere),this.cancelEdit(),alert("Matière mise à jour avec succès !")},cancelEdit(){this.editMode=!1,this.editedMatiere=null,this.editedIndex=-1},confirmDeleteMatiere(e){confirm("Êtes-vous sûr de vouloir supprimer cette matière ?")&&this.deleteMatiere(e)},deleteMatiere(e){this.matieres=this.matieres.filter((t=>t.id!==e)),alert("Matière supprimée avec succès !")}}},w=i(1241);const A=(0,w.A)(g,[["render",x],["__scopeId","data-v-5d8aec06"]]);var E=A}}]);
//# sourceMappingURL=936.6cd8949e.js.map