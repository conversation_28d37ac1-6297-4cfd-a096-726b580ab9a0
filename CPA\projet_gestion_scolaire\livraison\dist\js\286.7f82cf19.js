"use strict";(self["webpackChunkprojet_gestion_scolaire"]=self["webpackChunkprojet_gestion_scolaire"]||[]).push([[286],{3286:function(a,e,t){t.r(e),t.d(e,{default:function(){return R}});var s=t(6768),l=t(5130),d=t(4232);const i={class:"synthese-container"},n={class:"card"},r={class:"filters-form"},o={class:"form-group"},c={class:"form-group"},v={class:"form-group"},u={class:"form-actions"},f={key:0,class:"synthese-results"},k={class:"card mt-4"},m={class:"summary-cards"},p={class:"summary-card income"},L={class:"card-content"},b={class:"amount"},h={class:"summary-card expense"},y={class:"card-content"},g={class:"amount"},F={class:"summary-card balance"},A={class:"card-content"},D={class:"card mt-4"},M={class:"table-responsive"},_={class:"data-table"};function x(a,e,t,x,C,w){return(0,s.uX)(),(0,s.CE)("div",i,[e[28]||(e[28]=(0,s.Lk)("h1",null,"Synthèse Financière",-1)),(0,s.Lk)("div",n,[e[8]||(e[8]=(0,s.Lk)("h2",null,"Filtres",-1)),(0,s.Lk)("div",r,[(0,s.Lk)("div",o,[e[5]||(e[5]=(0,s.Lk)("label",{for:"periode"},"Période",-1)),(0,s.bo)((0,s.Lk)("select",{id:"periode","onUpdate:modelValue":e[0]||(e[0]=a=>C.selectedPeriode=a)},e[4]||(e[4]=[(0,s.Fv)('<option value="jour" data-v-4710345f>Journalier</option><option value="semaine" data-v-4710345f>Hebdomadaire</option><option value="mois" data-v-4710345f>Mensuel</option><option value="trimestre" data-v-4710345f>Trimestriel</option><option value="annee" data-v-4710345f>Annuel</option>',5)]),512),[[l.u1,C.selectedPeriode]])]),(0,s.Lk)("div",c,[e[6]||(e[6]=(0,s.Lk)("label",{for:"dateDebut"},"Date début",-1)),(0,s.bo)((0,s.Lk)("input",{type:"date",id:"dateDebut","onUpdate:modelValue":e[1]||(e[1]=a=>C.dateDebut=a)},null,512),[[l.Jo,C.dateDebut]])]),(0,s.Lk)("div",v,[e[7]||(e[7]=(0,s.Lk)("label",{for:"dateFin"},"Date fin",-1)),(0,s.bo)((0,s.Lk)("input",{type:"date",id:"dateFin","onUpdate:modelValue":e[2]||(e[2]=a=>C.dateFin=a)},null,512),[[l.Jo,C.dateFin]])]),(0,s.Lk)("div",u,[(0,s.Lk)("button",{onClick:e[3]||(e[3]=(...a)=>w.generateSynthese&&w.generateSynthese(...a)),class:"btn btn-primary"}," Générer la synthèse ")])])]),C.syntheseGenerated?((0,s.uX)(),(0,s.CE)("div",f,[(0,s.Lk)("div",k,[e[15]||(e[15]=(0,s.Lk)("h2",null,"Résumé des opérations financières",-1)),(0,s.Lk)("div",m,[(0,s.Lk)("div",p,[e[10]||(e[10]=(0,s.Lk)("div",{class:"card-icon"},[(0,s.Lk)("i",{class:"fas fa-money-bill-wave"})],-1)),(0,s.Lk)("div",L,[e[9]||(e[9]=(0,s.Lk)("h3",null,"Total Encaissements",-1)),(0,s.Lk)("p",b,(0,d.v_)(w.formatMontant(C.synthese.totalEncaissements))+" Ar",1)])]),(0,s.Lk)("div",h,[e[12]||(e[12]=(0,s.Lk)("div",{class:"card-icon"},[(0,s.Lk)("i",{class:"fas fa-file-invoice"})],-1)),(0,s.Lk)("div",y,[e[11]||(e[11]=(0,s.Lk)("h3",null,"Total Décaissements",-1)),(0,s.Lk)("p",g,(0,d.v_)(w.formatMontant(C.synthese.totalDecaissements))+" Ar",1)])]),(0,s.Lk)("div",F,[e[14]||(e[14]=(0,s.Lk)("div",{class:"card-icon"},[(0,s.Lk)("i",{class:"fas fa-balance-scale"})],-1)),(0,s.Lk)("div",A,[e[13]||(e[13]=(0,s.Lk)("h3",null,"Solde",-1)),(0,s.Lk)("p",{class:(0,d.C4)(["amount",{positive:C.synthese.solde>=0,negative:C.synthese.solde<0}])},(0,d.v_)(w.formatMontant(C.synthese.solde))+" Ar ",3)])])])]),e[26]||(e[26]=(0,s.Fv)('<div class="card mt-4" data-v-4710345f><h2 data-v-4710345f>Taux de paiement par classe</h2><div class="chart-container" data-v-4710345f><div class="chart-placeholder" data-v-4710345f><div class="bar-chart" data-v-4710345f><div class="bar-item" data-v-4710345f><div class="bar-label" data-v-4710345f>6ème A</div><div class="bar-value" style="width:85%;" data-v-4710345f>85%</div></div><div class="bar-item" data-v-4710345f><div class="bar-label" data-v-4710345f>6ème B</div><div class="bar-value" style="width:78%;" data-v-4710345f>78%</div></div><div class="bar-item" data-v-4710345f><div class="bar-label" data-v-4710345f>5ème A</div><div class="bar-value" style="width:92%;" data-v-4710345f>92%</div></div><div class="bar-item" data-v-4710345f><div class="bar-label" data-v-4710345f>5ème B</div><div class="bar-value" style="width:65%;" data-v-4710345f>65%</div></div></div></div></div></div><div class="card mt-4" data-v-4710345f><h2 data-v-4710345f>Répartition par mode de paiement</h2><div class="chart-container" data-v-4710345f><div class="pie-chart-container" data-v-4710345f><div class="pie-chart" data-v-4710345f><div class="pie-slice" style="--percentage:65;--color:#3f51b5;" data-v-4710345f><span class="pie-label" data-v-4710345f>Cash</span></div><div class="pie-slice" style="--percentage:35;--color:#f44336;" data-v-4710345f><span class="pie-label" data-v-4710345f>ADRA</span></div></div><div class="pie-legend" data-v-4710345f><div class="legend-item" data-v-4710345f><div class="legend-color" style="background-color:#3f51b5;" data-v-4710345f></div><div class="legend-label" data-v-4710345f>Cash: 65%</div></div><div class="legend-item" data-v-4710345f><div class="legend-color" style="background-color:#f44336;" data-v-4710345f></div><div class="legend-label" data-v-4710345f>ADRA: 35%</div></div></div></div></div></div>',2)),(0,s.Lk)("div",D,[e[25]||(e[25]=(0,s.Lk)("h2",null,"Répartition par motif",-1)),(0,s.Lk)("div",M,[(0,s.Lk)("table",_,[e[24]||(e[24]=(0,s.Lk)("thead",null,[(0,s.Lk)("tr",null,[(0,s.Lk)("th",null,"Motif"),(0,s.Lk)("th",null,"Montant"),(0,s.Lk)("th",null,"Pourcentage")])],-1)),(0,s.Lk)("tbody",null,[(0,s.Lk)("tr",null,[e[16]||(e[16]=(0,s.Lk)("td",null,"Frais de scolarité",-1)),(0,s.Lk)("td",null,(0,d.v_)(w.formatMontant(75e4))+" Ar",1),e[17]||(e[17]=(0,s.Lk)("td",null,"75%",-1))]),(0,s.Lk)("tr",null,[e[18]||(e[18]=(0,s.Lk)("td",null,"Frais d'examen",-1)),(0,s.Lk)("td",null,(0,d.v_)(w.formatMontant(15e4))+" Ar",1),e[19]||(e[19]=(0,s.Lk)("td",null,"15%",-1))]),(0,s.Lk)("tr",null,[e[20]||(e[20]=(0,s.Lk)("td",null,"Frais d'inscription",-1)),(0,s.Lk)("td",null,(0,d.v_)(w.formatMontant(1e5))+" Ar",1),e[21]||(e[21]=(0,s.Lk)("td",null,"10%",-1))])]),(0,s.Lk)("tfoot",null,[(0,s.Lk)("tr",null,[e[22]||(e[22]=(0,s.Lk)("th",null,"Total",-1)),(0,s.Lk)("th",null,(0,d.v_)(w.formatMontant(1e6))+" Ar",1),e[23]||(e[23]=(0,s.Lk)("th",null,"100%",-1))])])])])]),e[27]||(e[27]=(0,s.Fv)('<div class="export-actions mt-4" data-v-4710345f><button class="btn btn-secondary" data-v-4710345f><i class="fas fa-file-excel" data-v-4710345f></i> Export Excel </button><button class="btn btn-secondary" data-v-4710345f><i class="fas fa-file-pdf" data-v-4710345f></i> Export PDF </button></div>',1))])):(0,s.Q3)("",!0)])}var C={name:"FinanceSynthese",data(){return{selectedPeriode:"mois",dateDebut:"",dateFin:"",syntheseGenerated:!1,synthese:{totalEncaissements:1e6,totalDecaissements:47e4,solde:53e4,repartitionModes:{Cash:65e4,ADRA:35e4},repartitionMotifs:{"Frais de scolarité":75e4,"Frais d'examen":15e4,"Frais d'inscription":1e5},periode:"mois"}}},methods:{generateSynthese(){this.syntheseGenerated=!0,alert("Synthèse générée avec succès !")},formatMontant(a){return new Intl.NumberFormat("fr-FR").format(a)}}},w=t(1241);const E=(0,w.A)(C,[["render",x],["__scopeId","data-v-4710345f"]]);var R=E}}]);
//# sourceMappingURL=286.7f82cf19.js.map