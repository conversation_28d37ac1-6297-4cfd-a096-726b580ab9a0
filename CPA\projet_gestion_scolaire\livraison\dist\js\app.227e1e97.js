(function(){"use strict";var e={8036:function(e,t,n){var a={};n.r(a),n.d(a,{BulletinsService:function(){return ae},ClassesService:function(){return Z},ElevesService:function(){return Q},ExamensService:function(){return te},FinanceService:function(){return re},MatieresService:function(){return ee},NotesService:function(){return ne},StatistiquesService:function(){return se}});var r=n(1175),s=n(6768);const i={class:"app"},o={class:"main-container"},c={class:"content"};function l(e,t,n,a,r,l){const u=(0,s.g2)("Header"),d=(0,s.g2)("Sidebar"),m=(0,s.g2)("router-view"),f=(0,s.g2)("Footer");return(0,s.uX)(),(0,s.CE)("div",i,[(0,s.bF)(u),(0,s.Lk)("div",o,[(0,s.bF)(d),(0,s.Lk)("main",c,[(0,s.bF)(m)])]),(0,s.bF)(f)])}const u={class:"app-header"},d={class:"logo-container"};function m(e,t,n,a,r,i){return(0,s.uX)(),(0,s.CE)("header",u,[(0,s.Lk)("div",d,[(0,s.Q3)("",!0),t[0]||(t[0]=(0,s.Lk)("h1",null,"Collège Privé Adventiste Avaratetezana",-1))]),t[1]||(t[1]=(0,s.Lk)("div",{class:"user-menu"},[(0,s.Lk)("span",{class:"user-name"},"Administrateur"),(0,s.Lk)("button",{class:"logout-btn"},"Déconnexion")],-1))])}var f={name:"AppHeader"},E=n(1241);const h=(0,E.A)(f,[["render",m],["__scopeId","data-v-3e552c90"]]);var p=h;const y={class:"sidebar"},v={class:"sidebar-nav"},A={class:"menu-section"},g={class:"menu-section"},S={class:"menu-section"};function w(e,t,n,a,r,i){const o=(0,s.g2)("router-link");return(0,s.uX)(),(0,s.CE)("div",y,[t[13]||(t[13]=(0,s.Lk)("div",{class:"sidebar-header"},[(0,s.Lk)("h3",null,"Menu Principal")],-1)),(0,s.Lk)("nav",v,[(0,s.Lk)("ul",null,[(0,s.Lk)("li",null,[(0,s.bF)(o,{to:"/",exact:""},{default:(0,s.k6)((()=>t[0]||(t[0]=[(0,s.Lk)("i",{class:"fas fa-home"},null,-1),(0,s.eW)(" Tableau de bord ")]))),_:1})]),(0,s.Lk)("li",A,[t[4]||(t[4]=(0,s.Lk)("span",{class:"section-title"},"Finance",-1)),(0,s.Lk)("ul",null,[(0,s.Lk)("li",null,[(0,s.bF)(o,{to:"/finance/encaissements"},{default:(0,s.k6)((()=>t[1]||(t[1]=[(0,s.Lk)("i",{class:"fas fa-money-bill-wave"},null,-1),(0,s.eW)(" Encaissements ")]))),_:1})]),(0,s.Lk)("li",null,[(0,s.bF)(o,{to:"/finance/decaissements"},{default:(0,s.k6)((()=>t[2]||(t[2]=[(0,s.Lk)("i",{class:"fas fa-file-invoice"},null,-1),(0,s.eW)(" Décaissements ")]))),_:1})]),(0,s.Lk)("li",null,[(0,s.bF)(o,{to:"/finance/synthese"},{default:(0,s.k6)((()=>t[3]||(t[3]=[(0,s.Lk)("i",{class:"fas fa-chart-pie"},null,-1),(0,s.eW)(" Synthèse ")]))),_:1})])])]),(0,s.Lk)("li",g,[t[9]||(t[9]=(0,s.Lk)("span",{class:"section-title"},"Académique",-1)),(0,s.Lk)("ul",null,[(0,s.Lk)("li",null,[(0,s.bF)(o,{to:"/matieres"},{default:(0,s.k6)((()=>t[5]||(t[5]=[(0,s.Lk)("i",{class:"fas fa-book"},null,-1),(0,s.eW)(" Matières ")]))),_:1})]),(0,s.Lk)("li",null,[(0,s.bF)(o,{to:"/examens"},{default:(0,s.k6)((()=>t[6]||(t[6]=[(0,s.Lk)("i",{class:"fas fa-tasks"},null,-1),(0,s.eW)(" Examens ")]))),_:1})]),(0,s.Lk)("li",null,[(0,s.bF)(o,{to:"/notes"},{default:(0,s.k6)((()=>t[7]||(t[7]=[(0,s.Lk)("i",{class:"fas fa-pen"},null,-1),(0,s.eW)(" Saisie des notes ")]))),_:1})]),(0,s.Lk)("li",null,[(0,s.bF)(o,{to:"/bulletins"},{default:(0,s.k6)((()=>t[8]||(t[8]=[(0,s.Lk)("i",{class:"fas fa-file-alt"},null,-1),(0,s.eW)(" Bulletins ")]))),_:1})])])]),(0,s.Lk)("li",S,[t[12]||(t[12]=(0,s.Lk)("span",{class:"section-title"},"Analyses",-1)),(0,s.Lk)("ul",null,[(0,s.Lk)("li",null,[(0,s.bF)(o,{to:"/archives"},{default:(0,s.k6)((()=>t[10]||(t[10]=[(0,s.Lk)("i",{class:"fas fa-archive"},null,-1),(0,s.eW)(" Archives ")]))),_:1})]),(0,s.Lk)("li",null,[(0,s.bF)(o,{to:"/statistiques"},{default:(0,s.k6)((()=>t[11]||(t[11]=[(0,s.Lk)("i",{class:"fas fa-chart-line"},null,-1),(0,s.eW)(" Statistiques ")]))),_:1})])])])])])])}var I={name:"AppSidebar"};const L=(0,E.A)(I,[["render",w],["__scopeId","data-v-7ed111e6"]]);var T=L,b=n(4232);const _={class:"app-footer"},D={class:"footer-content"};function N(e,t,n,a,r,i){return(0,s.uX)(),(0,s.CE)("footer",_,[(0,s.Lk)("div",D,[(0,s.Lk)("p",null,"© "+(0,b.v_)(i.currentYear)+" Collège Privé Adventiste Avaratetezana - Tous droits réservés",1),t[0]||(t[0]=(0,s.Lk)("p",{class:"version"},"Version 1.0.0",-1))])])}var k={name:"AppFooter",computed:{currentYear(){return(new Date).getFullYear()}}};const C=(0,E.A)(k,[["render",N],["__scopeId","data-v-1024e7d1"]]);var M=C,O={name:"App",components:{Header:p,Sidebar:T,Footer:M},created(){this.$store.dispatch("fetchEleves"),this.$store.dispatch("fetchClasses"),this.$store.dispatch("fetchMatieres"),this.$store.dispatch("fetchExamens")}};const x=(0,E.A)(O,[["render",l]]);var F=x,P=n(1387);r["default"].use(P["default"]);const G=()=>n.e(569).then(n.bind(n,1569)),B=()=>Promise.all([n.e(130),n.e(275)]).then(n.bind(n,9275)),R=()=>Promise.all([n.e(130),n.e(593)]).then(n.bind(n,593)),$=()=>Promise.all([n.e(130),n.e(286)]).then(n.bind(n,3286)),j=()=>Promise.all([n.e(130),n.e(936)]).then(n.bind(n,4936)),q=()=>Promise.all([n.e(130),n.e(716)]).then(n.bind(n,4716)),U=()=>Promise.all([n.e(130),n.e(886)]).then(n.bind(n,2886)),W=()=>Promise.all([n.e(130),n.e(191)]).then(n.bind(n,7191)),X=()=>Promise.all([n.e(130),n.e(450)]).then(n.bind(n,9450)),H=()=>Promise.all([n.e(130),n.e(998)]).then(n.bind(n,998)),V=[{path:"/",name:"Dashboard",component:G},{path:"/finance/encaissements",name:"FinanceEncaissement",component:B},{path:"/finance/decaissements",name:"FinanceDecaissement",component:R},{path:"/finance/synthese",name:"FinanceSynthese",component:$},{path:"/matieres",name:"MatieresList",component:j},{path:"/examens",name:"ExamensList",component:q},{path:"/notes",name:"NotesGestion",component:U},{path:"/bulletins",name:"BulletinsGeneration",component:W},{path:"/archives",name:"ArchivesBulletins",component:X},{path:"/statistiques",name:"StatistiquesResultats",component:H},{path:"*",redirect:"/"}],Y=new P["default"]({mode:"history",base:"/",routes:V});var z=Y,J=(n(4114),n(8111),n(2489),n(116),n(8237),n(782));n(7588),n(1701),n(7642),n(8004),n(3853),n(5876),n(2475),n(5024),n(1698);const K=e=>new Promise((t=>setTimeout(t,e))),Q={async getAll(){return await K(300),[{id:1,nom:"Dupont",prenom:"Jean",matricule:"E001",classeId:1},{id:2,nom:"Martin",prenom:"Sophie",matricule:"E002",classeId:1},{id:3,nom:"Dubois",prenom:"Pierre",matricule:"E003",classeId:1},{id:4,nom:"Lefebvre",prenom:"Marie",matricule:"E004",classeId:2},{id:5,nom:"Moreau",prenom:"Lucas",matricule:"E005",classeId:2},{id:6,nom:"Petit",prenom:"Emma",matricule:"E006",classeId:3},{id:7,nom:"Robert",prenom:"Thomas",matricule:"E007",classeId:3}]},async getByClasse(e){const t=await this.getAll();return t.filter((t=>t.classeId===e))},async getById(e){const t=await this.getAll();return t.find((t=>t.id===e))}},Z={async getAll(){return await K(300),[{id:1,nom:"6ème A",effectif:35},{id:2,nom:"6ème B",effectif:32},{id:3,nom:"5ème A",effectif:30},{id:4,nom:"5ème B",effectif:28}]},async getById(e){const t=await this.getAll();return t.find((t=>t.id===e))}},ee={async getAll(){return await K(300),[{id:1,nom:"Mathématiques",abreviation:"MATH",coefficient:4},{id:2,nom:"Physique",abreviation:"PHY",coefficient:3},{id:3,nom:"Français",abreviation:"FR",coefficient:3},{id:4,nom:"Histoire-Géographie",abreviation:"HIST",coefficient:2},{id:5,nom:"Anglais",abreviation:"ANG",coefficient:2},{id:6,nom:"SVT",abreviation:"SVT",coefficient:2},{id:7,nom:"Éducation Physique",abreviation:"EPS",coefficient:1},{id:8,nom:"Arts Plastiques",abreviation:"ART",coefficient:1}]},async create(e){return await K(500),{...e,id:Date.now()}},async update(e,t){return await K(500),{...t}},async delete(e){return await K(500),{success:!0}}},te={async getAll(){return await K(300),[{id:1,type:"Trimestre 1",dateDebut:"2024-09-15",dateFin:"2024-10-30",classesIds:[1,2],verrouille:!0},{id:2,type:"Trimestre 1",dateDebut:"2024-09-15",dateFin:"2024-10-30",classesIds:[3,4],verrouille:!1},{id:3,type:"Trimestre 2",dateDebut:"2024-12-01",dateFin:"2025-01-15",classesIds:[1,2,3,4],verrouille:!1}]},async create(e){return await K(500),{...e,id:Date.now()}},async update(e,t){return await K(500),{...t,id:e}},async toggleVerrouillage(e){await K(300);const t=await this.getAll(),n=t.find((t=>t.id===e));if(n)return n.verrouille=!n.verrouille,n;throw new Error("Examen non trouvé")}},ne={async getByClasseAndExamen(e,t){await K(500);const n=await Q.getByClasse(e),a=await ee.getAll(),r={};return n.forEach((e=>{r[e.id]={},a.forEach((t=>{r[e.id][t.id]={ds1:Math.floor(10*Math.random())+10,ds2:Math.floor(10*Math.random())+10,examen:Math.floor(10*Math.random())+10}}))})),r},async saveNotes(){return await K(700),{success:!0,message:"Notes enregistrées avec succès"}},async calculateMoyennes(e,t){await K(500);const n=await Q.getByClasse(e),a=await ee.getAll(),r=await this.getByClasseAndExamen(e,t),s={},i={},o=a.reduce(((e,t)=>e+t.coefficient),0);n.forEach((e=>{let t=0;a.forEach((n=>{const a=r[e.id][n.id],s=(parseFloat(a.ds1)+parseFloat(a.ds2)+parseFloat(a.examen))/3,i=s*n.coefficient;t+=i})),s[e.id]=t/o,i[e.id]=t}));const c=Object.entries(s).map((([e,t])=>({eleveId:parseInt(e),moyenne:t}))).sort(((e,t)=>t.moyenne-e.moyenne)),l={};c.forEach(((e,t)=>{l[e.eleveId]=t+1}));const u=c.reduce(((e,t)=>e+t.moyenne),0)/c.length;return{moyennes:s,totauxPonderes:i,rangs:l,moyenneClasse:u,totalCoefficients:o}}},ae={async generer(e,t,n=null,a={}){await K(1e3);const r=await te.getAll().then((t=>t.find((t=>t.id===e)))),s=await Z.getById(t);let i=[];if(n){const e=await Q.getById(n);e&&(i=[e])}else i=await Q.getByClasse(t);const{moyennes:o,totauxPonderes:c,rangs:l,moyenneClasse:u,totalCoefficients:d}=await ne.calculateMoyennes(t,e),m=i.map((n=>({id:`${e}_${t}_${n.id}`,examenId:e,classeId:t,eleveId:n.id,eleveName:`${n.nom} ${n.prenom}`,matricule:n.matricule,classe:s.nom,examen:r.type,moyenne:o[n.id],totalPondere:c[n.id],rang:l[n.id],effectif:s.effectif,moyenneClasse:u,totalCoefficients:d,dateGeneration:(new Date).toISOString(),options:a})));return m},async getBulletinPDF(e){return await K(700),{url:`/bulletins/${e}.pdf`}}},re={async getEncaissements(e={}){await K(400);const t=[{id:1,date:"2024-03-15",eleveId:1,montant:5e4,motif:"Frais de scolarité",modePaiement:"Cash"},{id:2,date:"2024-03-20",eleveId:2,montant:25e3,motif:"Frais d'examen",modePaiement:"ADRA"},{id:3,date:"2024-03-22",eleveId:3,montant:5e4,motif:"Frais de scolarité",modePaiement:"Cash"},{id:4,date:"2024-03-25",eleveId:4,montant:15e3,motif:"Frais d'inscription",modePaiement:"ADRA"}];let n=[...t];if(e.dateDebut&&(n=n.filter((t=>new Date(t.date)>=new Date(e.dateDebut)))),e.dateFin&&(n=n.filter((t=>new Date(t.date)<=new Date(e.dateFin)))),e.searchTerm){const t=e.searchTerm.toLowerCase(),a=await Q.getAll();n=n.filter((e=>{const n=a.find((t=>t.id===e.eleveId));return n&&(n.nom.toLowerCase().includes(t)||n.prenom.toLowerCase().includes(t)||n.matricule.toLowerCase().includes(t))}))}return n},async saveEncaissement(e){return await K(500),{...e,id:Date.now()}},async getEncaissementRecu(e){return await K(300),{url:`/recus/${e}.pdf`}},async getDecaissements(e={}){await K(400);const t=[{id:1,date:"2024-03-10",beneficiaire:"Fournisseur Papeterie",montant:12e4,motif:"Achat fournitures",justificatif:"facture_001.pdf"},{id:2,date:"2024-03-18",beneficiaire:"JIRAMA",montant:35e4,motif:"Facture électricité",justificatif:"facture_jirama.pdf"}];let n=[...t];if(e.dateDebut&&(n=n.filter((t=>new Date(t.date)>=new Date(e.dateDebut)))),e.dateFin&&(n=n.filter((t=>new Date(t.date)<=new Date(e.dateFin)))),e.beneficiaire){const t=e.beneficiaire.toLowerCase();n=n.filter((e=>e.beneficiaire.toLowerCase().includes(t)))}return n},async saveDecaissement(e){return await K(500),{...e,id:Date.now()}},async getSynthese(e){await K(600);const t=await this.getEncaissements(),n=await this.getDecaissements(),a=t.reduce(((e,t)=>e+t.montant),0),r=n.reduce(((e,t)=>e+t.montant),0),s={Cash:t.filter((e=>"Cash"===e.modePaiement)).reduce(((e,t)=>e+t.montant),0),ADRA:t.filter((e=>"ADRA"===e.modePaiement)).reduce(((e,t)=>e+t.montant),0)},i=[...new Set(t.map((e=>e.motif)))],o={};return i.forEach((e=>{o[e]=t.filter((t=>t.motif===e)).reduce(((e,t)=>e+t.montant),0)})),{totalEncaissements:a,totalDecaissements:r,solde:a-r,repartitionModes:s,repartitionMotifs:o,periode:e}}},se={async getResultatsParMatiere(){await K(700);const e=await ee.getAll(),t={};return e.forEach((e=>{t[e.id]={matiere:e.nom,moyenne:(6*Math.random()+10).toFixed(2),tauxReussite:Math.floor(40*Math.random()+60)}})),t},async getEvolutionResultats(e){return await K(700),{trimestres:["Trimestre 1","Trimestre 2","Trimestre 3"],moyennes:[12.45,13.22,14.05],tauxReussite:[68,75,82]}},async getTopEleves(e,t=5){await K(500);const n=await Q.getAll(),a=n.slice(0,Math.min(t,n.length)).map((e=>({id:e.id,nom:`${e.nom} ${e.prenom}`,moyenne:(4*Math.random()+16).toFixed(2),classe:e.classeId}))).sort(((e,t)=>t.moyenne-e.moyenne));return a}};r["default"].use(J.Ay);var ie=new J.Ay.Store({state:{currentUser:{nom:"Administrateur",role:"admin"},isAuthenticated:!0,isLoading:!1,encaissements:[],decaissements:[],matieres:[],examens:[],notes:{},bulletins:[],eleves:[],classes:[],anneeScolaire:"2024-2025"},mutations:{SET_LOADING(e,t){e.isLoading=t},SET_ENCAISSEMENTS(e,t){e.encaissements=t},ADD_ENCAISSEMENT(e,t){e.encaissements.unshift(t)},SET_DECAISSEMENTS(e,t){e.decaissements=t},ADD_DECAISSEMENT(e,t){e.decaissements.unshift(t)},SET_MATIERES(e,t){e.matieres=t},ADD_MATIERE(e,t){e.matieres.push(t)},UPDATE_MATIERE(e,{id:t,matiere:n}){const a=e.matieres.findIndex((e=>e.id===t));-1!==a&&e.matieres.splice(a,1,n)},DELETE_MATIERE(e,t){e.matieres=e.matieres.filter((e=>e.id!==t))},SET_EXAMENS(e,t){e.examens=t},ADD_EXAMEN(e,t){e.examens.push(t)},UPDATE_EXAMEN(e,{id:t,examen:n}){const a=e.examens.findIndex((e=>e.id===t));-1!==a&&e.examens.splice(a,1,n)},SET_NOTES(e,{classeId:t,examenId:n,notes:a}){r["default"].set(e.notes,`${t}_${n}`,a)},UPDATE_NOTE(e,{classeId:t,examenId:n,eleveId:a,matiereId:r,note:s}){const i=`${t}_${n}`;e.notes[i]&&e.notes[i][a]&&e.notes[i][a][r]&&(e.notes[i][a][r]={...e.notes[i][a][r],...s})},SET_BULLETINS(e,t){e.bulletins=t},ADD_BULLETIN(e,t){e.bulletins.push(t)},SET_ELEVES(e,t){e.eleves=t},SET_CLASSES(e,t){e.classes=t},SET_ANNEE_SCOLAIRE(e,t){e.anneeScolaire=t}},actions:{async fetchEncaissements({commit:e},t={}){e("SET_LOADING",!0);try{const n=await re.getEncaissements(t);e("SET_ENCAISSEMENTS",n)}catch(n){console.error("Erreur lors de la récupération des encaissements:",n)}finally{e("SET_LOADING",!1)}},async saveEncaissement({commit:e},t){e("SET_LOADING",!0);try{const n=await re.saveEncaissement(t);return e("ADD_ENCAISSEMENT",n),n}catch(n){throw console.error("Erreur lors de l'enregistrement de l'encaissement:",n),n}finally{e("SET_LOADING",!1)}},async fetchMatieres({commit:e}){e("SET_LOADING",!0);try{const t=await ee.getAll();e("SET_MATIERES",t)}catch(t){console.error("Erreur lors de la récupération des matières:",t)}finally{e("SET_LOADING",!1)}},async createMatiere({commit:e},t){e("SET_LOADING",!0);try{const n=await ee.create(t);return e("ADD_MATIERE",n),n}catch(n){throw console.error("Erreur lors de la création de la matière:",n),n}finally{e("SET_LOADING",!1)}},async updateMatiere({commit:e},{id:t,matiere:n}){e("SET_LOADING",!0);try{const a=await ee.update(t,n);return e("UPDATE_MATIERE",{id:t,matiere:a}),a}catch(a){throw console.error("Erreur lors de la mise à jour de la matière:",a),a}finally{e("SET_LOADING",!1)}},async deleteMatiere({commit:e},t){e("SET_LOADING",!0);try{await ee.delete(t),e("DELETE_MATIERE",t)}catch(n){throw console.error("Erreur lors de la suppression de la matière:",n),n}finally{e("SET_LOADING",!1)}},async fetchExamens({commit:e}){e("SET_LOADING",!0);try{const t=await te.getAll();e("SET_EXAMENS",t)}catch(t){console.error("Erreur lors de la récupération des examens:",t)}finally{e("SET_LOADING",!1)}},async createExamen({commit:e},t){e("SET_LOADING",!0);try{const n=await te.create(t);return e("ADD_EXAMEN",n),n}catch(n){throw console.error("Erreur lors de la création de l'examen:",n),n}finally{e("SET_LOADING",!1)}},async toggleExamenVerrouillage({commit:e},t){e("SET_LOADING",!0);try{const n=await te.toggleVerrouillage(t);return e("UPDATE_EXAMEN",{id:t,examen:n}),n}catch(n){throw console.error("Erreur lors du verrouillage/déverrouillage de l'examen:",n),n}finally{e("SET_LOADING",!1)}},async fetchNotes({commit:e},{classeId:t,examenId:n}){e("SET_LOADING",!0);try{const a=await ne.getByClasseAndExamen(t,n);return e("SET_NOTES",{classeId:t,examenId:n,notes:a}),a}catch(a){throw console.error("Erreur lors de la récupération des notes:",a),a}finally{e("SET_LOADING",!1)}},async saveNotes({commit:e},{classeId:t,examenId:n,notes:a}){e("SET_LOADING",!0);try{return await ne.saveNotes(t,n,a),e("SET_NOTES",{classeId:t,examenId:n,notes:a}),{success:!0}}catch(r){throw console.error("Erreur lors de l'enregistrement des notes:",r),r}finally{e("SET_LOADING",!1)}},async calculateMoyennes({commit:e},{classeId:t,examenId:n}){e("SET_LOADING",!0);try{const e=await ne.calculateMoyennes(t,n);return e}catch(a){throw console.error("Erreur lors du calcul des moyennes:",a),a}finally{e("SET_LOADING",!1)}},async generateBulletins({commit:e},{examenId:t,classeId:n,eleveId:a,options:r}){e("SET_LOADING",!0);try{const s=await ae.generer(t,n,a,r);return e("SET_BULLETINS",s),s}catch(s){throw console.error("Erreur lors de la génération des bulletins:",s),s}finally{e("SET_LOADING",!1)}},async fetchEleves({commit:e}){e("SET_LOADING",!0);try{const t=await Q.getAll();e("SET_ELEVES",t)}catch(t){console.error("Erreur lors de la récupération des élèves:",t)}finally{e("SET_LOADING",!1)}},async fetchClasses({commit:e}){e("SET_LOADING",!0);try{const t=await Z.getAll();e("SET_CLASSES",t)}catch(t){console.error("Erreur lors de la récupération des classes:",t)}finally{e("SET_LOADING",!1)}},async fetchStatistiquesParMatiere({commit:e},t){e("SET_LOADING",!0);try{return await se.getResultatsParMatiere(t)}catch(n){throw console.error("Erreur lors de la récupération des statistiques par matière:",n),n}finally{e("SET_LOADING",!1)}},async fetchEvolutionResultats({commit:e},t){e("SET_LOADING",!0);try{return await se.getEvolutionResultats(t)}catch(n){throw console.error("Erreur lors de la récupération de l'évolution des résultats:",n),n}finally{e("SET_LOADING",!1)}}},getters:{totalEncaissements:e=>e.encaissements.reduce(((e,t)=>e+t.montant),0),encaissementsByPeriod:e=>(t,n)=>e.encaissements.filter((e=>{const a=new Date(e.date);return a>=t&&a<=n})),matieresSorted:e=>[...e.matieres].sort(((e,t)=>e.nom.localeCompare(t.nom))),notesByEleve:e=>(t,n,a)=>{const r=`${t}_${n}`;return e.notes[r]?e.notes[r][a]:null},elevesByClasse:e=>t=>e.eleves.filter((e=>e.classeId===t)),getEleveById:e=>t=>e.eleves.find((e=>e.id===t)),getClasseById:e=>t=>e.classes.find((e=>e.id===t)),isLoading:e=>e.isLoading}});r["default"].prototype.$api=a,r["default"].config.productionTip=!1,new r["default"]({router:z,store:ie,render:e=>e(F)}).$mount("#app")}},t={};function n(a){var r=t[a];if(void 0!==r)return r.exports;var s=t[a]={exports:{}};return e[a].call(s.exports,s,s.exports,n),s.exports}n.m=e,function(){var e=[];n.O=function(t,a,r,s){if(!a){var i=1/0;for(u=0;u<e.length;u++){a=e[u][0],r=e[u][1],s=e[u][2];for(var o=!0,c=0;c<a.length;c++)(!1&s||i>=s)&&Object.keys(n.O).every((function(e){return n.O[e](a[c])}))?a.splice(c--,1):(o=!1,s<i&&(i=s));if(o){e.splice(u--,1);var l=r();void 0!==l&&(t=l)}}return t}s=s||0;for(var u=e.length;u>0&&e[u-1][2]>s;u--)e[u]=e[u-1];e[u]=[a,r,s]}}(),function(){n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,{a:t}),t}}(),function(){n.d=function(e,t){for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}}(),function(){n.f={},n.e=function(e){return Promise.all(Object.keys(n.f).reduce((function(t,a){return n.f[a](e,t),t}),[]))}}(),function(){n.u=function(e){return"js/"+e+"."+{130:"dda0c49a",191:"fd9aa191",275:"fe649778",286:"7f82cf19",450:"6c636c5b",569:"0ac51137",593:"e11e7061",716:"51576e7c",886:"2371e518",936:"6cd8949e",998:"1cf200da"}[e]+".js"}}(),function(){n.miniCssF=function(e){return"css/"+e+"."+{191:"6747c71f",275:"9cc6ddc1",286:"78c42326",450:"c1d1b968",569:"9fbaaf3c",593:"03061211",716:"a2014a71",886:"e81da3e8",936:"cf5ae710",998:"42bec75a"}[e]+".css"}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="projet_gestion_scolaire:";n.l=function(a,r,s,i){if(e[a])e[a].push(r);else{var o,c;if(void 0!==s)for(var l=document.getElementsByTagName("script"),u=0;u<l.length;u++){var d=l[u];if(d.getAttribute("src")==a||d.getAttribute("data-webpack")==t+s){o=d;break}}o||(c=!0,o=document.createElement("script"),o.charset="utf-8",o.timeout=120,n.nc&&o.setAttribute("nonce",n.nc),o.setAttribute("data-webpack",t+s),o.src=a),e[a]=[r];var m=function(t,n){o.onerror=o.onload=null,clearTimeout(f);var r=e[a];if(delete e[a],o.parentNode&&o.parentNode.removeChild(o),r&&r.forEach((function(e){return e(n)})),t)return t(n)},f=setTimeout(m.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=m.bind(null,o.onerror),o.onload=m.bind(null,o.onload),c&&document.head.appendChild(o)}}}(),function(){n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){n.p="/"}(),function(){if("undefined"!==typeof document){var e=function(e,t,a,r,s){var i=document.createElement("link");i.rel="stylesheet",i.type="text/css",n.nc&&(i.nonce=n.nc);var o=function(n){if(i.onerror=i.onload=null,"load"===n.type)r();else{var a=n&&n.type,o=n&&n.target&&n.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+a+": "+o+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=a,c.request=o,i.parentNode&&i.parentNode.removeChild(i),s(c)}};return i.onerror=i.onload=o,i.href=t,a?a.parentNode.insertBefore(i,a.nextSibling):document.head.appendChild(i),i},t=function(e,t){for(var n=document.getElementsByTagName("link"),a=0;a<n.length;a++){var r=n[a],s=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(s===e||s===t))return r}var i=document.getElementsByTagName("style");for(a=0;a<i.length;a++){r=i[a],s=r.getAttribute("data-href");if(s===e||s===t)return r}},a=function(a){return new Promise((function(r,s){var i=n.miniCssF(a),o=n.p+i;if(t(i,o))return r();e(a,o,null,r,s)}))},r={524:0};n.f.miniCss=function(e,t){var n={191:1,275:1,286:1,450:1,569:1,593:1,716:1,886:1,936:1,998:1};r[e]?t.push(r[e]):0!==r[e]&&n[e]&&t.push(r[e]=a(e).then((function(){r[e]=0}),(function(t){throw delete r[e],t})))}}}(),function(){var e={524:0};n.f.j=function(t,a){var r=n.o(e,t)?e[t]:void 0;if(0!==r)if(r)a.push(r[2]);else{var s=new Promise((function(n,a){r=e[t]=[n,a]}));a.push(r[2]=s);var i=n.p+n.u(t),o=new Error,c=function(a){if(n.o(e,t)&&(r=e[t],0!==r&&(e[t]=void 0),r)){var s=a&&("load"===a.type?"missing":a.type),i=a&&a.target&&a.target.src;o.message="Loading chunk "+t+" failed.\n("+s+": "+i+")",o.name="ChunkLoadError",o.type=s,o.request=i,r[1](o)}};n.l(i,c,"chunk-"+t,t)}},n.O.j=function(t){return 0===e[t]};var t=function(t,a){var r,s,i=a[0],o=a[1],c=a[2],l=0;if(i.some((function(t){return 0!==e[t]}))){for(r in o)n.o(o,r)&&(n.m[r]=o[r]);if(c)var u=c(n)}for(t&&t(a);l<i.length;l++)s=i[l],n.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return n.O(u)},a=self["webpackChunkprojet_gestion_scolaire"]=self["webpackChunkprojet_gestion_scolaire"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))}();var a=n.O(void 0,[504],(function(){return n(8036)}));a=n.O(a)})();
//# sourceMappingURL=app.227e1e97.js.map