.container {
    font-size: 11px;
}
@media (min-width: 768px) {
    .container {
        width: 750px;
    }
}
@media (min-width: 992px) {
    .container {
        width: 970px;
    }
}

@media (min-width: 1200px) {
    .container {
        width: 1170px;
    }
}

@media print {

    td, th {
        padding: 1px;
        text-align: center;
        font-size: 10px;
    }

    @page {
        size: A4 landscape !important;   /* Définir le format A4 en orientation paysage */
        margin: 0;  /* this affects the margin in the printer settings */
    }
    
    /* Forcer l'orientation paysage */
    @media print {
        html, body {
            width: 297mm !important;
            height: 210mm !important;
            overflow: hidden !important;
        }
        
        .container {
            width: 100% !important;
            height: 100% !important;
            transform-origin: top left;
            transform: scale(0.98); /* Légère réduction pour s'assurer que tout tient */
        }
    }

    html {
        background-color: #FFFFFF;
        margin: 5px; /* Réduire la marge */
    }

    body {
        margin: 0 5mm; /* Rédu<PERSON> la marge */
    }
    
    /* Optimisations pour tenir sur une page */
    table {
        margin-bottom: 5px !important;
    }
    
    h4 {
        margin-top: 5px !important;
        margin-bottom: 5px !important;
    }
    
    div {
        margin-top: 0 !important;
        margin-bottom: 5px !important;
    }
}

td {
    text-align: center;
}

table.td-left td{
    text-align: left!important;
    padding: 5px;
}
