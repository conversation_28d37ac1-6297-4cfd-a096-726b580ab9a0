/* ------------------------------------------------------------------------------
 *
 *  # Modern Theme - CSS améliorations pour CPA
 *
 *  Fichier CSS personnalisé pour rendre l'interface plus moderne, dynamique et claire
 *
 * ---------------------------------------------------------------------------- */

/* Variables globales */
:root {
    --primary-color: #4361ee;
    --primary-light: #4895ef;
    --primary-dark: #3f37c9;
    --secondary-color: #4cc9f0;
    --success-color: #4ade80;
    --info-color: #60a5fa;
    --warning-color: #fbbf24;
    --danger-color: #f87171;
    --light-color: #f9fafb;
    --dark-color: #1f2937;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --border-radius: 0.375rem;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --transition-speed: 0.2s;
}

/* Styles généraux */
body {
    background-color: #f5f7fa;
    color: var(--gray-700);
}

/* Améliorations de la barre de navigation */
.navbar {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.navbar-dark {
    background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
}

/* Améliorations des cartes */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid var(--gray-200);
    padding: 1.25rem 1.5rem;
}

.card-title {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0;
}

.card-body {
    padding: 1.5rem;
}

/* Améliorations des tableaux */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--gray-700);
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    font-weight: 600;
    background-color: var(--gray-100);
    color: var(--gray-800);
    border-top: none;
    padding: 0.75rem 1rem;
    vertical-align: middle;
}

.table td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
    border-top: 1px solid var(--gray-200);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.table-responsive {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    background-color: #fff;
}

/* Améliorations des boutons */
.btn {
    font-weight: 500;
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    transition: all var(--transition-speed);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* Badges améliorés */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 0.25rem;
}

.badge-primary {
    background-color: var(--primary-color);
}

.badge-success {
    background-color: var(--success-color);
}

.badge-info {
    background-color: var(--info-color);
}

.badge-warning {
    background-color: var(--warning-color);
    color: var(--gray-800);
}

.badge-danger {
    background-color: var(--danger-color);
}

/* Améliorations des formulaires */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-300);
    padding: 0.5rem 0.75rem;
    transition: border-color var(--transition-speed), box-shadow var(--transition-speed);
}

.form-control:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

/* Améliorations des onglets */
.nav-tabs {
    border-bottom: 1px solid var(--gray-300);
}

.nav-tabs .nav-link {
    border: none;
    color: var(--gray-600);
    padding: 0.75rem 1rem;
    font-weight: 500;
    transition: all var(--transition-speed);
}

.nav-tabs .nav-link:hover {
    color: var(--primary-color);
    border-color: transparent;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: transparent;
    border-bottom: 2px solid var(--primary-color);
}

.tab-content {
    padding: 1.5rem 0;
}

/* Améliorations de la barre latérale */
.sidebar {
    background-color: #fff;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
}

.sidebar .nav-sidebar .nav-item-header {
    padding: 1.25rem 1.25rem 0.5rem;
    color: var(--gray-500);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.sidebar .nav-sidebar .nav-link {
    color: var(--gray-700);
    padding: 0.75rem 1.25rem;
    border-radius: 0.25rem;
    margin: 0.125rem 0.75rem;
    transition: all var(--transition-speed);
}

.sidebar .nav-sidebar .nav-link:hover {
    color: var(--primary-color);
    background-color: var(--gray-100);
}

.sidebar .nav-sidebar .nav-link.active {
    color: var(--primary-color);
    background-color: rgba(67, 97, 238, 0.1);
}

.sidebar .nav-sidebar .nav-link i {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    opacity: 0.75;
}

/* Améliorations du tableau de bord */
.dashboard-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dashboard-card .card-body {
    padding: 1.5rem;
}

.dashboard-card .icon-box {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.2);
}

.dashboard-card .icon-box i {
    font-size: 1.5rem;
    color: #fff;
}

/* Améliorations des alertes */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    background-color: rgba(74, 222, 128, 0.1);
    color: var(--success-color);
}

.alert-info {
    background-color: rgba(96, 165, 250, 0.1);
    color: var(--info-color);
}

.alert-warning {
    background-color: rgba(251, 191, 36, 0.1);
    color: #92400e;
}

.alert-danger {
    background-color: rgba(248, 113, 113, 0.1);
    color: var(--danger-color);
}

/* Animations et transitions */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Améliorations spécifiques pour les pages de paiement */
.payment-summary {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.payment-summary-title {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--gray-200);
}

.payment-summary-table {
    width: 100%;
}

.payment-summary-table td {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.payment-summary-table tr:last-child td {
    border-bottom: none;
}

.payment-summary-table .amount-label {
    font-weight: 500;
    color: var(--gray-700);
}

.payment-summary-table .amount-value {
    font-weight: 600;
    color: var(--gray-800);
    text-align: right;
}

.payment-summary-table .highlight-row td {
    background-color: var(--gray-100);
    font-weight: 600;
}

/* Améliorations pour les tableaux de données */
.datatable-header {
    background-color: #fff;
    padding: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.datatable-footer {
    background-color: #fff;
    padding: 1rem;
    border-top: 1px solid var(--gray-200);
}

.dataTables_info {
    color: var(--gray-600);
}

.dataTables_paginate .paginate_button {
    border-radius: var(--border-radius);
    margin: 0 0.125rem;
}

.dataTables_paginate .paginate_button.current {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: #fff !important;
}

/* Améliorations pour les pages d'étudiants */
.student-profile {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.student-profile-header {
    background: linear-gradient(to right, var(--primary-color), var(--primary-light));
    padding: 2rem;
    color: #fff;
    text-align: center;
}

.student-profile-img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 5px solid rgba(255, 255, 255, 0.3);
    object-fit: cover;
    margin-bottom: 1rem;
}

.student-profile-body {
    padding: 1.5rem;
}

.student-profile-info {
    margin-bottom: 1.5rem;
}

.student-profile-info-title {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--gray-200);
}

/* Améliorations pour les pages de notes */
.marks-table th {
    position: sticky;
    top: 0;
    background-color: var(--gray-100);
    z-index: 10;
}

.marks-table td {
    text-align: center;
}

.marks-table .student-name {
    position: sticky;
    left: 0;
    background-color: #fff;
    z-index: 5;
    font-weight: 500;
}

.marks-table .student-name.highlighted {
    background-color: var(--gray-100);
}

/* Améliorations pour les impressions */
@media print {
    body {
        background-color: #fff;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
    
    .table-responsive {
        box-shadow: none;
    }
    
    .no-print {
        display: none !important;
    }
}
