"use strict";(self["webpackChunkprojet_gestion_scolaire"]=self["webpackChunkprojet_gestion_scolaire"]||[]).push([[191],{7191:function(e,l,t){t.r(l),t.d(l,{default:function(){return Y}});var s=t(6768),a=t(4232),n=t(5130);const i={class:"bulletins-container"},r={class:"card"},o={class:"generation-form"},d={class:"form-group"},u=["value"],c={class:"form-group"},v=["disabled"],m=["value"],p={class:"form-group"},k=["disabled"],f=["value"],h={class:"form-group"},L={class:"form-group"},b={class:"checkbox-item"},g={class:"checkbox-item"},E={class:"checkbox-item"},C={class:"form-actions"},I=["disabled"],y=["disabled"],x={key:0,class:"card mt-4"},w={class:"card-header-actions"},A={class:"header-actions"},_={class:"bulletins-list"},P={class:"bulletin-info"},B={class:"bulletin-name"},F={class:"bulletin-details"},G={key:1,class:"bulletin-preview-overlay"},S={class:"bulletin-preview"},D={class:"preview-header"},q={class:"preview-content"},X={class:"bulletin-header"},M={class:"school-info"},R={class:"student-info"},T={class:"bulletin-body"},N={class:"grades-table"},V={key:0,class:"bulletin-chart"},W={key:1,class:"bulletin-appreciation"},H={class:"bulletin-footer"},U={class:"signature-block"},$={key:0,class:"signature-item"},Q={key:1,class:"signature-item"},K={class:"preview-actions"};function j(e,l,t,j,z,J){return(0,s.uX)(),(0,s.CE)("div",i,[l[47]||(l[47]=(0,s.Lk)("h1",null,"Génération des Bulletins",-1)),(0,s.Lk)("div",r,[l[27]||(l[27]=(0,s.Lk)("h2",null,"Paramètres de génération",-1)),(0,s.Lk)("div",o,[(0,s.Lk)("div",d,[l[14]||(l[14]=(0,s.Lk)("label",{for:"examen"},"Examen",-1)),(0,s.bo)((0,s.Lk)("select",{id:"examen","onUpdate:modelValue":l[0]||(l[0]=e=>z.selectedExamenId=e),required:""},[l[13]||(l[13]=(0,s.Lk)("option",{value:"",disabled:""},"Sélectionner un examen",-1)),((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(z.examens,(e=>((0,s.uX)(),(0,s.CE)("option",{key:e.id,value:e.id},(0,a.v_)(e.type)+" ("+(0,a.v_)(J.formatDate(e.dateDebut))+" - "+(0,a.v_)(J.formatDate(e.dateFin))+") ",9,u)))),128))],512),[[n.u1,z.selectedExamenId]])]),(0,s.Lk)("div",c,[l[16]||(l[16]=(0,s.Lk)("label",{for:"classe"},"Classe",-1)),(0,s.bo)((0,s.Lk)("select",{id:"classe","onUpdate:modelValue":l[1]||(l[1]=e=>z.selectedClasseId=e),disabled:!z.selectedExamenId,required:""},[l[15]||(l[15]=(0,s.Lk)("option",{value:"",disabled:""},"Sélectionner une classe",-1)),((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(J.classesForExamen,(e=>((0,s.uX)(),(0,s.CE)("option",{key:e.id,value:e.id},(0,a.v_)(e.nom)+" ("+(0,a.v_)(e.effectif)+" élèves) ",9,m)))),128))],8,v),[[n.u1,z.selectedClasseId]])]),(0,s.Lk)("div",p,[l[18]||(l[18]=(0,s.Lk)("label",{for:"eleve"},"Élève (optionnel)",-1)),(0,s.bo)((0,s.Lk)("select",{id:"eleve","onUpdate:modelValue":l[2]||(l[2]=e=>z.selectedEleveId=e),disabled:!z.selectedClasseId},[l[17]||(l[17]=(0,s.Lk)("option",{value:""},"Tous les élèves",-1)),((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(J.elevesForClasse,(e=>((0,s.uX)(),(0,s.CE)("option",{key:e.id,value:e.id},(0,a.v_)(e.nom)+" "+(0,a.v_)(e.prenom)+" ("+(0,a.v_)(e.matricule)+") ",9,f)))),128))],8,k),[[n.u1,z.selectedEleveId]])]),(0,s.Lk)("div",h,[l[20]||(l[20]=(0,s.Lk)("label",{for:"format"},"Format",-1)),(0,s.bo)((0,s.Lk)("select",{id:"format","onUpdate:modelValue":l[3]||(l[3]=e=>z.format=e),required:""},l[19]||(l[19]=[(0,s.Lk)("option",{value:"A5"},"A5 Portrait",-1),(0,s.Lk)("option",{value:"A4"},"A4 Portrait",-1)]),512),[[n.u1,z.format]])]),(0,s.Lk)("div",L,[l[24]||(l[24]=(0,s.Lk)("label",null,"Options",-1)),(0,s.Lk)("div",b,[(0,s.bo)((0,s.Lk)("input",{type:"checkbox",id:"inclureSignature","onUpdate:modelValue":l[4]||(l[4]=e=>z.options.inclureSignature=e)},null,512),[[n.lH,z.options.inclureSignature]]),l[21]||(l[21]=(0,s.Lk)("label",{for:"inclureSignature"},"Inclure signature du directeur",-1))]),(0,s.Lk)("div",g,[(0,s.bo)((0,s.Lk)("input",{type:"checkbox",id:"inclureGraphique","onUpdate:modelValue":l[5]||(l[5]=e=>z.options.inclureGraphique=e)},null,512),[[n.lH,z.options.inclureGraphique]]),l[22]||(l[22]=(0,s.Lk)("label",{for:"inclureGraphique"},"Inclure graphique de progression",-1))]),(0,s.Lk)("div",E,[(0,s.bo)((0,s.Lk)("input",{type:"checkbox",id:"inclureAppreciation","onUpdate:modelValue":l[6]||(l[6]=e=>z.options.inclureAppreciation=e)},null,512),[[n.lH,z.options.inclureAppreciation]]),l[23]||(l[23]=(0,s.Lk)("label",{for:"inclureAppreciation"},"Inclure appréciation générale",-1))])]),(0,s.Lk)("div",C,[(0,s.Lk)("button",{onClick:l[7]||(l[7]=(...e)=>J.previewBulletin&&J.previewBulletin(...e)),class:"btn btn-secondary",disabled:!J.canGenerateBulletin},l[25]||(l[25]=[(0,s.Lk)("i",{class:"fas fa-eye"},null,-1),(0,s.eW)(" Aperçu ")]),8,I),(0,s.Lk)("button",{onClick:l[8]||(l[8]=(...e)=>J.generateBulletins&&J.generateBulletins(...e)),class:"btn btn-primary",disabled:!J.canGenerateBulletin},l[26]||(l[26]=[(0,s.Lk)("i",{class:"fas fa-file-pdf"},null,-1),(0,s.eW)(" Générer PDF ")]),8,y)])])]),z.bulletinsGenerated?((0,s.uX)(),(0,s.CE)("div",x,[(0,s.Lk)("div",w,[l[29]||(l[29]=(0,s.Lk)("h2",null,"Bulletins générés",-1)),(0,s.Lk)("div",A,[(0,s.Lk)("button",{onClick:l[9]||(l[9]=(...e)=>J.downloadAllBulletins&&J.downloadAllBulletins(...e)),class:"btn btn-primary"},l[28]||(l[28]=[(0,s.Lk)("i",{class:"fas fa-download"},null,-1),(0,s.eW)(" Télécharger tout ")]))])]),(0,s.Lk)("div",_,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(z.generatedBulletins,((e,t)=>((0,s.uX)(),(0,s.CE)("div",{key:t,class:"bulletin-item"},[(0,s.Lk)("div",P,[(0,s.Lk)("span",B,(0,a.v_)(e.eleveName),1),(0,s.Lk)("span",F,(0,a.v_)(e.classe)+" - "+(0,a.v_)(e.examen),1)]),l[30]||(l[30]=(0,s.Fv)('<div class="bulletin-actions" data-v-52310e90><button class="btn-icon" title="Aperçu" data-v-52310e90><i class="fas fa-eye" data-v-52310e90></i></button><button class="btn-icon" title="Télécharger" data-v-52310e90><i class="fas fa-download" data-v-52310e90></i></button><button class="btn-icon" title="Imprimer" data-v-52310e90><i class="fas fa-print" data-v-52310e90></i></button></div>',1))])))),128))])])):(0,s.Q3)("",!0),z.showPreview?((0,s.uX)(),(0,s.CE)("div",G,[(0,s.Lk)("div",S,[(0,s.Lk)("div",D,[l[32]||(l[32]=(0,s.Lk)("h3",null,"Aperçu du bulletin",-1)),(0,s.Lk)("button",{onClick:l[10]||(l[10]=(...e)=>J.closePreview&&J.closePreview(...e)),class:"btn-close"},l[31]||(l[31]=[(0,s.Lk)("i",{class:"fas fa-times"},null,-1)]))]),(0,s.Lk)("div",q,[(0,s.Lk)("div",{class:(0,a.C4)(["bulletin-template",z.format])},[(0,s.Lk)("div",X,[(0,s.Lk)("div",M,[l[33]||(l[33]=(0,s.Lk)("h2",null,"Collège Privé Adventiste Avaratetezana",-1)),l[34]||(l[34]=(0,s.Lk)("p",null,"Année scolaire: 2024-2025",-1)),(0,s.Lk)("p",null,(0,a.v_)(J.currentExamen?J.currentExamen.type:""),1)]),(0,s.Lk)("div",R,[(0,s.Lk)("p",null,[l[35]||(l[35]=(0,s.Lk)("strong",null,"Nom et prénom:",-1)),(0,s.eW)(" "+(0,a.v_)(J.currentEleve?`${J.currentEleve.nom} ${J.currentEleve.prenom}`:"Dupont Jean"),1)]),(0,s.Lk)("p",null,[l[36]||(l[36]=(0,s.Lk)("strong",null,"Matricule:",-1)),(0,s.eW)(" "+(0,a.v_)(J.currentEleve?J.currentEleve.matricule:"E001"),1)]),(0,s.Lk)("p",null,[l[37]||(l[37]=(0,s.Lk)("strong",null,"Classe:",-1)),(0,s.eW)(" "+(0,a.v_)(J.currentClasse?J.currentClasse.nom:"6ème A"),1)]),(0,s.Lk)("p",null,[l[38]||(l[38]=(0,s.Lk)("strong",null,"Effectif:",-1)),(0,s.eW)(" "+(0,a.v_)(J.currentClasse?J.currentClasse.effectif:"35"),1)])])]),(0,s.Lk)("div",T,[(0,s.Lk)("table",N,[l[39]||(l[39]=(0,s.Lk)("thead",null,[(0,s.Lk)("tr",null,[(0,s.Lk)("th",null,"Matière (Coeff)"),(0,s.Lk)("th",null,"DS1 (/20)"),(0,s.Lk)("th",null,"DS2 (/20)"),(0,s.Lk)("th",null,"Examen (/20)"),(0,s.Lk)("th",null,"Moyenne (/20)"),(0,s.Lk)("th",null,"Total Pondéré"),(0,s.Lk)("th",null,"Remarques")])],-1)),(0,s.Lk)("tbody",null,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(z.matieres,((e,l)=>((0,s.uX)(),(0,s.CE)("tr",{key:l},[(0,s.Lk)("td",null,(0,a.v_)(e.nom)+" ("+(0,a.v_)(e.coefficient)+")",1),(0,s.Lk)("td",null,(0,a.v_)(J.getRandomNote()),1),(0,s.Lk)("td",null,(0,a.v_)(J.getRandomNote()),1),(0,s.Lk)("td",null,(0,a.v_)(J.getRandomNote()),1),(0,s.Lk)("td",null,(0,a.v_)(J.getRandomAverage()),1),(0,s.Lk)("td",null,(0,a.v_)((J.getRandomAverage()*e.coefficient).toFixed(1)),1),(0,s.Lk)("td",null,(0,a.v_)(J.getRandomRemark()),1)])))),128))])]),l[42]||(l[42]=(0,s.Fv)('<div class="bulletin-summary" data-v-52310e90><div class="summary-item" data-v-52310e90><span data-v-52310e90>Somme des Totaux Pondérés:</span><span data-v-52310e90>287.5</span></div><div class="summary-item" data-v-52310e90><span data-v-52310e90>Somme des Coefficients:</span><span data-v-52310e90>18</span></div><div class="summary-item" data-v-52310e90><span data-v-52310e90>Moyenne Générale Élève:</span><span data-v-52310e90>15.97/20</span></div><div class="summary-item" data-v-52310e90><span data-v-52310e90>Moyenne de la Classe:</span><span data-v-52310e90>14.23/20</span></div><div class="summary-item" data-v-52310e90><span data-v-52310e90>Rang:</span><span data-v-52310e90>5ème/35</span></div></div>',1)),z.options.inclureGraphique?((0,s.uX)(),(0,s.CE)("div",V,l[40]||(l[40]=[(0,s.Lk)("p",{class:"chart-title"},"Progression trimestrielle",-1),(0,s.Lk)("div",{class:"chart-placeholder"}," [Graphique de progression] ",-1)]))):(0,s.Q3)("",!0),z.options.inclureAppreciation?((0,s.uX)(),(0,s.CE)("div",W,l[41]||(l[41]=[(0,s.Lk)("p",{class:"appreciation-title"},"Appréciation générale",-1),(0,s.Lk)("p",{class:"appreciation-content"},"Élève sérieux et appliqué. Bons résultats dans l'ensemble. Poursuivez vos efforts.",-1)]))):(0,s.Q3)("",!0)]),(0,s.Lk)("div",H,[(0,s.Lk)("div",U,[l[45]||(l[45]=(0,s.Lk)("div",{class:"signature-item"},[(0,s.Lk)("p",null,"Professeur Principal"),(0,s.Lk)("div",{class:"signature-line"})],-1)),z.options.inclureSignature?((0,s.uX)(),(0,s.CE)("div",$,l[43]||(l[43]=[(0,s.Lk)("p",null,"Directrice",-1),(0,s.Lk)("div",{class:"signature-line"},null,-1)]))):(0,s.Q3)("",!0),z.options.inclureSignature?((0,s.uX)(),(0,s.CE)("div",Q,l[44]||(l[44]=[(0,s.Lk)("p",null,"Cachet de l'établissement",-1),(0,s.Lk)("div",{class:"signature-stamp"},null,-1)]))):(0,s.Q3)("",!0)])])],2)]),(0,s.Lk)("div",K,[(0,s.Lk)("button",{onClick:l[11]||(l[11]=(...e)=>J.closePreview&&J.closePreview(...e)),class:"btn btn-secondary"},"Fermer"),(0,s.Lk)("button",{onClick:l[12]||(l[12]=(...e)=>J.generateBulletins&&J.generateBulletins(...e)),class:"btn btn-primary"},l[46]||(l[46]=[(0,s.Lk)("i",{class:"fas fa-file-pdf"},null,-1),(0,s.eW)(" Générer PDF ")]))])])])):(0,s.Q3)("",!0)])}t(4114),t(8111),t(2489),t(116),t(7588);var z={name:"BulletinsGeneration",data(){return{selectedExamenId:"",selectedClasseId:"",selectedEleveId:"",format:"A5",options:{inclureSignature:!0,inclureGraphique:!1,inclureAppreciation:!0},examens:[{id:1,type:"Trimestre 1",dateDebut:"2024-09-15",dateFin:"2024-10-30",classesIds:[1,2],verrouille:!0},{id:2,type:"Trimestre 1",dateDebut:"2024-09-15",dateFin:"2024-10-30",classesIds:[3,4],verrouille:!1}],classes:[{id:1,nom:"6ème A",effectif:35},{id:2,nom:"6ème B",effectif:32},{id:3,nom:"5ème A",effectif:30},{id:4,nom:"5ème B",effectif:28}],eleves:[{id:1,nom:"Dupont",prenom:"Jean",matricule:"E001",classeId:1},{id:2,nom:"Martin",prenom:"Sophie",matricule:"E002",classeId:1},{id:3,nom:"Dubois",prenom:"Pierre",matricule:"E003",classeId:1},{id:4,nom:"Lefebvre",prenom:"Marie",matricule:"E004",classeId:2},{id:5,nom:"Moreau",prenom:"Lucas",matricule:"E005",classeId:2}],matieres:[{id:1,nom:"Mathématiques",abreviation:"MATH",coefficient:4},{id:2,nom:"Physique",abreviation:"PHY",coefficient:3},{id:3,nom:"Français",abreviation:"FR",coefficient:3},{id:4,nom:"Histoire-Géographie",abreviation:"HIST",coefficient:2},{id:5,nom:"Anglais",abreviation:"ANG",coefficient:2},{id:6,nom:"SVT",abreviation:"SVT",coefficient:2},{id:7,nom:"Éducation Physique",abreviation:"EPS",coefficient:1},{id:8,nom:"Arts Plastiques",abreviation:"ART",coefficient:1}],showPreview:!1,bulletinsGenerated:!1,generatedBulletins:[]}},computed:{classesForExamen(){if(!this.selectedExamenId)return[];const e=this.examens.find((e=>e.id===this.selectedExamenId));return e?this.classes.filter((l=>e.classesIds.includes(l.id))):[]},elevesForClasse(){return this.selectedClasseId?this.eleves.filter((e=>e.classeId===this.selectedClasseId)):[]},canGenerateBulletin(){return this.selectedExamenId&&this.selectedClasseId},currentExamen(){return this.examens.find((e=>e.id===this.selectedExamenId))},currentClasse(){return this.classes.find((e=>e.id===this.selectedClasseId))},currentEleve(){return this.selectedEleveId?this.eleves.find((e=>e.id===this.selectedEleveId)):null}},methods:{previewBulletin(){this.showPreview=!0},closePreview(){this.showPreview=!1},generateBulletins(){if(this.generatedBulletins=[],this.selectedEleveId){const e=this.eleves.find((e=>e.id===this.selectedEleveId)),l=this.classes.find((e=>e.id===this.selectedClasseId)),t=this.examens.find((e=>e.id===this.selectedExamenId));e&&l&&t&&this.generatedBulletins.push({id:Date.now(),eleveId:e.id,eleveName:`${e.nom} ${e.prenom}`,classe:l.nom,examen:t.type,format:this.format,options:{...this.options}})}else{const e=this.classes.find((e=>e.id===this.selectedClasseId)),l=this.examens.find((e=>e.id===this.selectedExamenId)),t=this.eleves.filter((e=>e.classeId===this.selectedClasseId));e&&l&&t.forEach((t=>{this.generatedBulletins.push({id:Date.now()+t.id,eleveId:t.id,eleveName:`${t.nom} ${t.prenom}`,classe:e.nom,examen:l.type,format:this.format,options:{...this.options}})}))}this.bulletinsGenerated=!0,this.showPreview=!1,alert(`${this.generatedBulletins.length} bulletin(s) généré(s) avec succès !`)},downloadAllBulletins(){alert("Téléchargement de tous les bulletins en cours...")},formatDate(e){const l={day:"2-digit",month:"2-digit",year:"numeric"};return new Date(e).toLocaleDateString("fr-FR",l)},getRandomNote(){return(Math.floor(10*Math.random())+10).toString()},getRandomAverage(){return(Math.floor(1e3*Math.random())/100+10).toFixed(2)},getRandomRemark(){const e=["Bon travail","Peut mieux faire","Excellents résultats","En progrès","Efforts à poursuivre"];return e[Math.floor(Math.random()*e.length)]}}},J=t(1241);const O=(0,J.A)(z,[["render",j],["__scopeId","data-v-52310e90"]]);var Y=O}}]);
//# sourceMappingURL=191.fd9aa191.js.map