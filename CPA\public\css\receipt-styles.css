/* 
 * Styles CSS pour les reçus de paiement
 * Optimisé pour impression thermique et affichage numérique
 * Version: 2.0
 */

/* Configuration de base pour impression thermique */
@page {
    size: 58mm auto;
    margin: 0;
}

/* Styles de base */
.receipt-container {
    font-family: 'Courier New', monospace;
    margin: 0;
    padding: 0;
    width: 58mm;
    font-size: 12px;
    font-weight: bold;
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    line-height: 1.2;
    background-color: white;
}

/* Application globale du gras pour tous les éléments critiques */
.receipt-container,
.receipt-container .container,
.receipt-container .header,
.receipt-container .receipt-info,
.receipt-container .student-info,
.receipt-container .payment-info,
.receipt-container .payment-table,
.receipt-container .payment-table th,
.receipt-container .payment-table td,
.receipt-container .footer,
.receipt-container .sign,
.receipt-container .cut-line,
.receipt-container strong,
.receipt-container td,
.receipt-container th,
.receipt-container div,
.receipt-container span,
.receipt-container .amount,
.receipt-container .date,
.receipt-container .critical-info {
    font-weight: bold !important;
}

/* Conteneur principal */
.receipt-container .container {
    width: 56mm;
    margin: 0 auto;
    padding: 1mm;
}

/* En-tête avec hiérarchie typographique claire */
.receipt-container .header {
    text-align: center;
    font-size: 16px;
    font-weight: 900;
    margin-bottom: 1mm;
    border-bottom: 2px solid #000;
    padding-bottom: 1mm;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.receipt-container .receipt-title {
    text-align: center;
    font-size: 14px;
    font-weight: 900;
    margin: 1mm 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    background-color: #f0f0f0;
    padding: 1mm;
    border: 1px solid #000;
}

/* Informations du reçu */
.receipt-container .receipt-info {
    font-size: 11px;
    margin-bottom: 1mm;
    border-bottom: 1px solid #ccc;
    padding-bottom: 1mm;
}

.receipt-container .receipt-info .ref-number {
    font-size: 12px;
    font-weight: 900;
    text-align: center;
    background-color: #f8f8f8;
    padding: 0.5mm;
    border: 1px solid #000;
    margin-bottom: 1mm;
}

/* Informations étudiant */
.receipt-container .student-info {
    font-size: 11px;
    margin-bottom: 1mm;
    border-bottom: 1px solid #ccc;
    padding: 1mm;
    background-color: #fafafa;
}

.receipt-container .student-info p {
    margin: 0.5mm 0;
    font-weight: bold;
}

.receipt-container .student-info .student-name {
    font-size: 12px;
    font-weight: 900;
}

/* Badges de statut */
.receipt-container .status-badge {
    display: inline-block;
    padding: 1mm 2mm;
    border-radius: 1mm;
    font-size: 11px;
    font-weight: 900;
    text-transform: uppercase;
    margin-top: 1mm;
    border: 1px solid #000;
}

.receipt-container .status-normal {
    background-color: #d4edda;
    color: #155724;
    border-color: #155724;
}

.receipt-container .status-adra {
    background-color: #cce7ff;
    color: #004085;
    border-color: #004085;
}

.receipt-container .status-team3 {
    background-color: #fff3cd;
    color: #856404;
    border-color: #856404;
}

/* Historique des paiements */
.receipt-container .payment-history {
    font-size: 11px;
    width: 100%;
    margin-bottom: 1mm;
    border: 1px solid #000;
    background-color: #fafafa;
}

.receipt-container .payment-history-title {
    text-align: center;
    font-weight: 900;
    margin-bottom: 1mm;
    font-size: 13px;
    background-color: #e9ecef;
    padding: 1mm;
    border-bottom: 1px solid #000;
    text-transform: uppercase;
}

.receipt-container .payment-duration {
    text-align: center;
    font-size: 10px;
    font-weight: bold;
    margin-bottom: 1mm;
    padding: 0.5mm;
    background-color: #f8f9fa;
    border-bottom: 1px dotted #666;
}

.receipt-container .payment-history-table {
    width: 100%;
    border-collapse: collapse;
}

.receipt-container .payment-history-table th,
.receipt-container .payment-history-table td {
    border-bottom: 1px dotted #666;
    padding: 1mm 0.5mm;
    font-size: 10px;
    font-weight: bold;
}

.receipt-container .payment-history-table th {
    font-weight: 900;
    font-size: 11px;
    background-color: #e9ecef;
    text-transform: uppercase;
}

.receipt-container .payment-history-table td.text-right {
    text-align: right;
}

.receipt-container .payment-history-table .amount {
    font-weight: 900;
    font-size: 11px;
}

.receipt-container .payment-history-table .date {
    font-weight: 900;
}

/* Résumé de paiement */
.receipt-container .payment-summary {
    margin: 1mm 0;
    border: 2px solid #000;
    border-radius: 1mm;
    background-color: #f8f9fa;
}

.receipt-container .payment-summary-title {
    text-align: center;
    font-weight: 900;
    background-color: #343a40;
    color: white;
    border-bottom: 1px solid #000;
    padding: 1mm;
    font-size: 13px;
    text-transform: uppercase;
}

.receipt-container .payment-summary-table {
    width: 100%;
    border-collapse: collapse;
}

.receipt-container .payment-summary-table tr td {
    padding: 1mm;
    font-size: 11px;
    font-weight: bold;
}

.receipt-container .payment-summary-table tr:not(:last-child) td {
    border-bottom: 1px dotted #666;
}

.receipt-container .payment-summary-table .amount-label {
    font-weight: 900;
}

.receipt-container .payment-summary-table .amount-value {
    text-align: right;
    font-weight: 900;
    font-size: 12px;
}

.receipt-container .payment-summary-table .highlight-row td {
    background-color: #fff3cd;
    font-weight: 900;
    font-size: 13px;
    border: 1px solid #856404;
}

/* Pied de page */
.receipt-container .footer {
    text-align: center;
    font-size: 12px;
    margin: 1mm 0;
    font-weight: 900;
    padding: 1mm;
    border: 2px solid #000;
    border-radius: 1mm;
    background-color: #f8f9fa;
}

.receipt-container .footer .critical-amount {
    font-size: 16px;
    font-weight: 900;
    margin-top: 1mm;
    padding: 1mm;
    background-color: #fff;
    border: 1px solid #000;
    border-radius: 0.5mm;
}

.receipt-container .footer .payment-date {
    font-size: 13px;
    font-weight: 900;
    margin-top: 1mm;
}

/* Signature */
.receipt-container .sign {
    text-align: center;
    font-size: 11px;
    margin-top: 1mm;
    padding-top: 1mm;
    border-top: 1px solid #666;
    font-weight: 900;
}

.receipt-container .cashier-info {
    font-size: 12px;
    font-weight: 900;
}

/* Ligne de découpe */
.receipt-container .cut-line {
    border-top: 2px dashed #000;
    margin: 2mm 0;
    height: 1mm;
    position: relative;
}

.receipt-container .cut-line:after {
    content: '✂ DÉCOUPER ICI ✂';
    position: absolute;
    top: -2mm;
    left: 50%;
    transform: translateX(-50%);
    font-size: 8px;
    background-color: white;
    padding: 0 1mm;
    font-weight: bold;
}

/* Date et heure */
.receipt-container .date-time {
    font-size: 11px;
    text-align: center;
    margin-top: 1mm;
    font-weight: 900;
}

.receipt-container .current-datetime {
    font-size: 10px;
    font-weight: bold;
    color: #666;
    margin-top: 0.5mm;
}

/* Classes utilitaires */
.receipt-container .text-center {
    text-align: center;
}

.receipt-container .text-right {
    text-align: right;
}

.receipt-container .text-bold {
    font-weight: 900 !important;
}

.receipt-container .amount {
    font-weight: 900 !important;
    font-size: 12px !important;
}

.receipt-container .date {
    font-weight: bold !important;
}

/* Optimisations pour impression thermique */
@media print {
    .receipt-container * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    
    .receipt-container .payment-summary,
    .receipt-container .footer {
        border: 1px solid #000 !important;
    }
    
    .receipt-container .status-badge {
        border: 1px solid #000 !important;
    }
    
    .receipt-container .payment-summary-table .highlight-row td {
        font-size: 14px !important;
    }
    
    .receipt-container .payment-summary-table tr td {
        font-size: 12px !important;
    }
    
    .receipt-container .footer .critical-amount {
        font-size: 18px !important;
    }
    
    .receipt-container .cashier-info {
        font-size: 14px !important;
    }
    
    /* Masquer les éléments non nécessaires à l'impression */
    .no-print {
        display: none !important;
    }
}

/* Responsive pour affichage numérique */
@media screen and (min-width: 768px) {
    .receipt-container {
        width: auto;
        max-width: 350px;
        margin: 20px auto;
        padding: 20px;
        border: 2px solid #ddd;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        background-color: #fff;
    }
    
    .receipt-container .container {
        width: 100%;
        padding: 0;
    }
    
    .receipt-container .header {
        font-size: 18px;
        padding-bottom: 2mm;
    }
    
    .receipt-container .receipt-title {
        font-size: 16px;
        padding: 2mm;
    }
    
    .receipt-container .payment-history-title {
        font-size: 15px;
    }
    
    .receipt-container .footer .critical-amount {
        font-size: 18px;
    }
}

/* Styles pour mode sombre */
@media (prefers-color-scheme: dark) {
    .receipt-container {
        background-color: #1a1a1a;
        color: #fff;
    }
    
    .receipt-container .receipt-title,
    .receipt-container .payment-history,
    .receipt-container .payment-summary,
    .receipt-container .footer {
        background-color: #2d2d2d;
        border-color: #555;
    }
    
    .receipt-container .payment-summary-title {
        background-color: #444;
        color: #fff;
    }
}

/* Animation pour l'impression */
.receipt-container.printing {
    animation: fadeOut 0.3s ease-in-out;
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0.8; }
}

/* Styles pour les erreurs de formatage */
.receipt-container .error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    padding: 1mm;
    margin: 1mm 0;
    border-radius: 0.5mm;
    font-size: 10px;
    text-align: center;
}

/* Styles pour les messages de succès */
.receipt-container .success-message {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    padding: 1mm;
    margin: 1mm 0;
    border-radius: 0.5mm;
    font-size: 10px;
    text-align: center;
}