{"version": 3, "file": "js/886.2371e518.js", "mappings": "qOACOA,MAAM,mB,GAGJA,MAAM,Q,GAEJA,MAAM,kB,GACJA,MAAM,c,EAPnB,U,GAiBaA,MAAM,c,EAjBnB,U,GA2BaA,MAAM,gB,EA3BnB,a,GAAAC,IAAA,EAmC4BD,MAAM,a,GAavBA,MAAM,oB,GACFA,MAAM,0B,GAoBHA,MAAM,c,EArExB,mC,EAAA,mC,EAAA,mC,GA2GkBA,MAAM,W,GACNA,MAAM,Q,GAMbA,MAAM,qB,EAlHjB,a,GAAAC,IAAA,EA4HgDD,MAAM,4B,0CA3HpDE,EAAAA,EAAAA,IA8HM,MA9HNC,EA8HM,gBA7HJC,EAAAA,EAAAA,IAAyB,UAArB,oBAAgB,KAEpBA,EAAAA,EAAAA,IA6BM,MA7BNC,EA6BM,gBA5BJD,EAAAA,EAAAA,IAA8C,UAA1C,yCAAqC,KACzCA,EAAAA,EAAAA,IA0BM,MA1BNE,EA0BM,EAzBJF,EAAAA,EAAAA,IAQM,MARNG,EAQM,cAPJH,EAAAA,EAAAA,IAAkC,SAA3BI,IAAI,UAAS,UAAM,cAC1BJ,EAAAA,EAAAA,IAKS,UALDK,GAAG,SATrB,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GASuCC,EAAAC,iBAAgBF,GAAEG,SAAA,I,cAC7CV,EAAAA,EAAAA,IAAyD,UAAjDW,MAAM,GAAGC,SAAA,IAAS,0BAAsB,mBAChDd,EAAAA,EAAAA,IAESe,EAAAA,GAAA,MAbrBC,EAAAA,EAAAA,IAWqCN,EAAAO,SAAVC,K,WAAflB,EAAAA,EAAAA,IAES,UAF0BD,IAAKmB,EAAOX,GAAKM,MAAOK,EAAOX,K,QAC7DW,EAAOC,MAAO,MAAEC,EAAAA,EAAAA,IAAGC,EAAAC,WAAWJ,EAAOK,YAAa,OAAGH,EAAAA,EAAAA,IAAGC,EAAAC,WAAWJ,EAAOM,UAAW,KAC1F,EAbZC,M,mBASuCf,EAAAC,uBAQ/BT,EAAAA,EAAAA,IAQM,MARNwB,EAQM,gBAPJxB,EAAAA,EAAAA,IAAkC,SAA3BI,IAAI,UAAS,UAAM,cAC1BJ,EAAAA,EAAAA,IAKS,UALDK,GAAG,SAnBrB,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GAmBuCC,EAAAiB,iBAAgBlB,GAAEG,SAAA,I,gBAC7CV,EAAAA,EAAAA,IAA0D,UAAlDW,MAAM,GAAGC,SAAA,IAAS,2BAAuB,mBACjDd,EAAAA,EAAAA,IAESe,EAAAA,GAAA,MAvBrBC,EAAAA,EAAAA,IAqBqCK,EAAAO,kBAAVC,K,WAAf7B,EAAAA,EAAAA,IAES,UAFmCD,IAAK8B,EAAOtB,GAAKM,MAAOgB,EAAOtB,K,QACtEsB,EAAOC,KAAM,MAAEV,EAAAA,EAAAA,IAAGS,EAAOE,UAAW,YACzC,EAvBZC,M,mBAmBuCtB,EAAAiB,uBAQ/BzB,EAAAA,EAAAA,IAIM,MAJN+B,EAIM,EAHJ/B,EAAAA,EAAAA,IAES,UAFAgC,QAAK1B,EAAA,KAAAA,EAAA,OAAA2B,IAAEd,EAAAe,WAAAf,EAAAe,aAAAD,IAAWrC,MAAM,kBAAmBgB,UAAWO,EAAAgB,cAAc,sBAE7E,EA9BVC,SAmCe5B,EAAA6B,cAAW,WAAtBvC,EAAAA,EAAAA,IAuFM,MAvFNwC,EAuFM,gBA1HVC,EAAAA,EAAAA,IAAA,wZAgDMvC,EAAAA,EAAAA,IAgEM,MAhENwC,EAgEM,EA/DJxC,EAAAA,EAAAA,IA8DQ,QA9DRyC,EA8DQ,EA7DNzC,EAAAA,EAAAA,IAgBQ,eAfNA,EAAAA,EAAAA,IAOK,0BANHA,EAAAA,EAAAA,IAA6C,MAAzC0C,QAAQ,IAAI9C,MAAM,cAAa,SAAK,mBACxCE,EAAAA,EAAAA,IAEKe,EAAAA,GAAA,MAvDnBC,EAAAA,EAAAA,IAqDoCN,EAAAmC,UAAXC,K,WAAX9C,EAAAA,EAAAA,IAEK,MAF4BD,IAAK+C,EAAQvC,GAAKwC,QAAS,EAAGjD,MAAM,mB,QAChEgD,EAAQhB,KAAM,YAAQV,EAAAA,EAAAA,IAAG0B,EAAQE,aAAc,KACpD,M,qBACA9C,EAAAA,EAAAA,IAA4B,MAAxB0C,QAAQ,KAAI,WAAO,mBACvB1C,EAAAA,EAAAA,IAAyB,MAArB0C,QAAQ,KAAI,QAAI,OAEtB1C,EAAAA,EAAAA,IAMK,0BALHF,EAAAA,EAAAA,IAIWe,EAAAA,GAAA,MAhEzBC,EAAAA,EAAAA,IA4D0CN,EAAAmC,UAAXC,K,WA5D/B9C,EAAAA,EAAAA,IAAAe,EAAAA,GAAA,CAAAhB,IAAA,OA4DiE+C,EAAQvC,M,gBACzDL,EAAAA,EAAAA,IAAY,UAAR,OAAG,mBACPA,EAAAA,EAAAA,IAAY,UAAR,OAAG,mBACPA,EAAAA,EAAAA,IAAe,UAAX,UAAM,Y,WAIhBA,EAAAA,EAAAA,IA2CQ,6BA1CNF,EAAAA,EAAAA,IAyCKe,EAAAA,GAAA,MA7GjBC,EAAAA,EAAAA,IAoEkCN,EAAAuC,QAAVC,K,WAAZlD,EAAAA,EAAAA,IAyCK,MAzC0BD,IAAKmD,EAAM3C,I,EACxCL,EAAAA,EAAAA,IAA8D,KAA9DiD,GAA8D/B,EAAAA,EAAAA,IAApC8B,EAAMpB,KAAM,KAACV,EAAAA,EAAAA,IAAG8B,EAAME,QAAM,kBAEtDpD,EAAAA,EAAAA,IAkCWe,EAAAA,GAAA,MAzGzBC,EAAAA,EAAAA,IAuE0CN,EAAAmC,UAAXC,K,WAvE/B9C,EAAAA,EAAAA,IAAAe,EAAAA,GAAA,CAAAhB,IAAA,QAuEkEmD,EAAM3C,MAAMuC,EAAQvC,M,EACtEL,EAAAA,EAAAA,IAUK,qBATHA,EAAAA,EAAAA,IAQC,SAPCiB,KAAK,SACLkC,IAAI,IACJC,IAAI,KACJC,KAAK,OA7EzB,sBAAA9C,GA8E6BC,EAAA8C,UAAUN,EAAM3C,IAAIuC,EAAQvC,IAAIkD,IAAGhD,EAC3CiD,SAAMlD,EAAA,KAAAA,EAAA,GAAAC,GAAEY,EAAAsC,qBACR7C,SAAUJ,EAAAkD,kB,QAhF/BC,GAAA,OA8E6BnD,EAAA8C,UAAUN,EAAM3C,IAAIuC,EAAQvC,IAAIkD,UAK7CvD,EAAAA,EAAAA,IAUK,qBATHA,EAAAA,EAAAA,IAQC,SAPCiB,KAAK,SACLkC,IAAI,IACJC,IAAI,KACJC,KAAK,OAxFzB,sBAAA9C,GAyF6BC,EAAA8C,UAAUN,EAAM3C,IAAIuC,EAAQvC,IAAIuD,IAAGrD,EAC3CiD,SAAMlD,EAAA,KAAAA,EAAA,GAAAC,GAAEY,EAAAsC,qBACR7C,SAAUJ,EAAAkD,kB,QA3F/BG,GAAA,OAyF6BrD,EAAA8C,UAAUN,EAAM3C,IAAIuC,EAAQvC,IAAIuD,UAK7C5D,EAAAA,EAAAA,IAUK,qBATHA,EAAAA,EAAAA,IAQC,SAPCiB,KAAK,SACLkC,IAAI,IACJC,IAAI,KACJC,KAAK,OAnGzB,sBAAA9C,GAoG6BC,EAAA8C,UAAUN,EAAM3C,IAAIuC,EAAQvC,IAAIW,OAAMT,EAC9CiD,SAAMlD,EAAA,KAAAA,EAAA,GAAAC,GAAEY,EAAAsC,qBACR7C,SAAUJ,EAAAkD,kB,QAtG/BI,GAAA,OAoG6BtD,EAAA8C,UAAUN,EAAM3C,IAAIuC,EAAQvC,IAAIW,aAAM,O,OAOrDhB,EAAAA,EAAAA,IAAuF,KAAvF+D,GAAuF7C,EAAAA,EAAAA,IAAhEV,EAAAwD,SAAShB,EAAM3C,IAAMG,EAAAwD,SAAShB,EAAM3C,IAAI4D,QAAQ,GAAK,KAAN,IACtEjE,EAAAA,EAAAA,IAAkD,KAAlDkE,GAAkDhD,EAAAA,EAAAA,IAA9BV,EAAA2D,MAAMnB,EAAM3C,KAAO,KAAL,Q,aAM1CL,EAAAA,EAAAA,IAOM,MAPNoE,EAOM,EANJpE,EAAAA,EAAAA,IAES,UAFAgC,QAAK1B,EAAA,KAAAA,EAAA,OAAA2B,IAAEd,EAAAkD,WAAAlD,EAAAkD,aAAApC,IAAWrC,MAAM,kBAAmBgB,SAAUJ,EAAAkD,kBAAkB,0BAEhF,EArHRY,GAsHuB9D,EAAAkD,kBAtHvBa,EAAAA,EAAAA,IAAA,SAsHuC,WAA/BzE,EAAAA,EAAAA,IAES,UAxHjBD,IAAA,EAsH0CmC,QAAK1B,EAAA,KAAAA,EAAA,OAAA2B,IAAEd,EAAAqD,eAAArD,EAAAqD,iBAAAvC,IAAerC,MAAM,mBAAkB,mCAtHxF2E,EAAAA,EAAAA,IAAA,OA4He/D,EAAAkD,kBAAoBlD,EAAA6B,cAAW,WAA1CvC,EAAAA,EAAAA,IAEM,MAFN2E,EAEMnE,EAAA,MAAAA,EAAA,MADJN,EAAAA,EAAAA,IAA2B,KAAxBJ,MAAM,eAAa,UA7H5B8E,EAAAA,EAAAA,IA6HiC,+EA7HjCH,EAAAA,EAAAA,IAAA,Q,4CAmIA,GACEI,KAAM,eACNC,IAAAA,GACE,MAAO,CACLnE,iBAAkB,GAClBgB,iBAAkB,GAClBY,aAAa,EACbqB,kBAAkB,EAElB3C,QAAS,CACP,CACEV,GAAI,EACJY,KAAM,cACNI,UAAW,aACXC,QAAS,aACTuD,WAAY,CAAC,EAAG,GAChBC,YAAY,GAEd,CACEzE,GAAI,EACJY,KAAM,cACNI,UAAW,aACXC,QAAS,aACTuD,WAAY,CAAC,EAAG,GAChBC,YAAY,IAIhBC,QAAS,CACP,CAAE1E,GAAI,EAAGuB,IAAK,SAAUC,SAAU,IAClC,CAAExB,GAAI,EAAGuB,IAAK,SAAUC,SAAU,IAClC,CAAExB,GAAI,EAAGuB,IAAK,SAAUC,SAAU,IAClC,CAAExB,GAAI,EAAGuB,IAAK,SAAUC,SAAU,KAGpCc,SAAU,CACR,CAAEtC,GAAI,EAAGuB,IAAK,gBAAiBoD,YAAa,OAAQlC,YAAa,GACjE,CAAEzC,GAAI,EAAGuB,IAAK,WAAYoD,YAAa,MAAOlC,YAAa,GAC3D,CAAEzC,GAAI,EAAGuB,IAAK,WAAYoD,YAAa,KAAMlC,YAAa,IAG5DC,OAAQ,CACN,CAAE1C,GAAI,EAAGuB,IAAK,SAAUsB,OAAQ,OAAQ+B,UAAW,OAAQC,SAAU,GACrE,CAAE7E,GAAI,EAAGuB,IAAK,SAAUsB,OAAQ,SAAU+B,UAAW,OAAQC,SAAU,GACvE,CAAE7E,GAAI,EAAGuB,IAAK,SAAUsB,OAAQ,SAAU+B,UAAW,OAAQC,SAAU,IAGzE5B,UAAW,CAAC,EACZU,SAAU,CAAC,EACXG,MAAO,CAAC,EAEZ,EAEAgB,SAAU,CACRzD,gBAAAA,GACE,IAAK0D,KAAK3E,iBAAkB,MAAO,GAEnC,MAAMO,EAASoE,KAAKrE,QAAQsE,MAAKC,GAAKA,EAAEjF,KAAO+E,KAAK3E,mBACpD,OAAKO,EAEEoE,KAAKL,QAAQQ,QAAOC,GAAKxE,EAAO6D,WAAWY,SAASD,EAAEnF,MAFzC,EAGtB,EAEA8B,YAAAA,GACE,OAAOiD,KAAK3E,kBAAoB2E,KAAK3D,gBACvC,GAGFiE,QAAS,CACPxD,SAAAA,GAKE,MAAMlB,EAASoE,KAAKrE,QAAQsE,MAAKC,GAAKA,EAAEjF,KAAO+E,KAAK3E,mBACpD2E,KAAK1B,mBAAmB1C,GAASA,EAAO8D,WAGxCM,KAAKrC,OAAS,CACZ,CAAE1C,GAAI,EAAGuB,IAAK,SAAUsB,OAAQ,OAAQ+B,UAAW,OAAQC,SAAUE,KAAK3D,kBAC1E,CAAEpB,GAAI,EAAGuB,IAAK,SAAUsB,OAAQ,SAAU+B,UAAW,OAAQC,SAAUE,KAAK3D,kBAC5E,CAAEpB,GAAI,EAAGuB,IAAK,SAAUsB,OAAQ,SAAU+B,UAAW,OAAQC,SAAUE,KAAK3D,mBAI9E2D,KAAK9B,UAAY,CAAC,EAClB8B,KAAKrC,OAAO4C,SAAQ3C,IAClBoC,KAAK9B,UAAUN,EAAM3C,IAAM,CAAC,EAC5B+E,KAAKzC,SAASgD,SAAQ/C,IAEpBwC,KAAK9B,UAAUN,EAAM3C,IAAIuC,EAAQvC,IAAM,CACrCkD,IAAKqC,KAAKC,MAAsB,GAAhBD,KAAKE,UAAiB,GACtClC,IAAKgC,KAAKC,MAAsB,GAAhBD,KAAKE,UAAiB,GACtC9E,OAAQ4E,KAAKC,MAAsB,GAAhBD,KAAKE,UAAiB,GAC1C,GACD,IAGJV,KAAK3B,oBACL2B,KAAK/C,aAAc,CACrB,EAEAoB,iBAAAA,GAEE2B,KAAKpB,SAAW,CAAC,EAEjBoB,KAAKrC,OAAO4C,SAAQ3C,IAClB,IAAI+C,EAAc,EACdC,EAAoB,EAExBZ,KAAKzC,SAASgD,SAAQ/C,IACpB,MAAMqD,EAAQb,KAAK9B,UAAUN,EAAM3C,IAAIuC,EAAQvC,IACzC6F,GAAWC,WAAWF,EAAM1C,KAAO4C,WAAWF,EAAMrC,KAAOuC,WAAWF,EAAMjF,SAAW,EACvFoF,EAASF,EAAUtD,EAAQE,YAEjCiD,GAAeK,EACfJ,GAAqBpD,EAAQE,WAAW,IAG1CsC,KAAKpB,SAAShB,EAAM3C,IAAM0F,EAAcC,CAAiB,IAI3D,MAAMK,EAAgBC,OAAOC,QAAQnB,KAAKpB,UACvCwC,KAAI,EAAEC,EAASP,MAAa,CAAGO,QAASC,SAASD,GAAUP,cAC3DS,MAAK,CAACC,EAAGC,IAAMA,EAAEX,QAAUU,EAAEV,UAEhCd,KAAKjB,MAAQ,CAAC,EACdkC,EAAcV,SAAQ,CAACmB,EAAMC,KAC3B3B,KAAKjB,MAAM2C,EAAKL,SAAWM,EAAQ,CAAC,GAExC,EAEA1C,SAAAA,GAEE2C,MAAM,mCACR,EAEAxC,aAAAA,GACE,GAAIyC,QAAQ,8FAA+F,CAEzG,MAAMjG,EAASoE,KAAKrE,QAAQsE,MAAKC,GAAKA,EAAEjF,KAAO+E,KAAK3E,mBAChDO,IACFA,EAAO8D,YAAa,EACpBM,KAAK1B,kBAAmB,GAG1BsD,MAAM,+CACR,CACF,EAEA5F,UAAAA,CAAW8F,GACT,MAAMC,EAAU,CAAEC,IAAK,UAAWC,MAAO,UAAWC,KAAM,WAC1D,OAAO,IAAIC,KAAKL,GAAYM,mBAAmB,QAASL,EAC1D,I,UCtRJ,MAAMM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://projet_gestion_scolaire/./src/views/notes/NotesGestion.vue", "webpack://projet_gestion_scolaire/./src/views/notes/NotesGestion.vue?1447"], "sourcesContent": ["<template>\n  <div class=\"notes-container\">\n    <h1>Saisie des Notes</h1>\n    \n    <div class=\"card\">\n      <h2>Sélection de l'examen et de la classe</h2>\n      <div class=\"selection-form\">\n        <div class=\"form-group\">\n          <label for=\"examen\">Examen</label>\n          <select id=\"examen\" v-model=\"selectedExamenId\" required>\n            <option value=\"\" disabled>Sélectionner un examen</option>\n            <option v-for=\"examen in examens\" :key=\"examen.id\" :value=\"examen.id\">\n              {{ examen.type }} ({{ formatDate(examen.dateDebut) }} - {{ formatDate(examen.dateFin) }})\n            </option>\n          </select>\n        </div>\n        \n        <div class=\"form-group\">\n          <label for=\"classe\">Classe</label>\n          <select id=\"classe\" v-model=\"selectedClasseId\" required>\n            <option value=\"\" disabled>Sélectionner une classe</option>\n            <option v-for=\"classe in classesForExamen\" :key=\"classe.id\" :value=\"classe.id\">\n              {{ classe.nom }} ({{ classe.effectif }} élèves)\n            </option>\n          </select>\n        </div>\n        \n        <div class=\"form-actions\">\n          <button @click=\"loadNotes\" class=\"btn btn-primary\" :disabled=\"!canLoadNotes\">\n            Charger les notes\n          </button>\n        </div>\n      </div>\n    </div>\n    \n    <div v-if=\"notesLoaded\" class=\"card mt-4\">\n      <div class=\"card-header-actions\">\n        <h2>Tableau de saisie des notes</h2>\n        <div class=\"header-actions\">\n          <button class=\"btn btn-secondary\">\n            <i class=\"fas fa-file-excel\"></i> Importer Excel\n          </button>\n          <button class=\"btn btn-secondary\">\n            <i class=\"fas fa-file-export\"></i> Exporter\n          </button>\n        </div>\n      </div>\n      \n      <div class=\"table-responsive\">\n        <table class=\"data-table notes-table\">\n          <thead>\n            <tr>\n              <th rowspan=\"2\" class=\"sticky-col\">Élève</th>\n              <th v-for=\"matiere in matieres\" :key=\"matiere.id\" :colspan=\"3\" class=\"matiere-header\">\n                {{ matiere.nom }} (Coef. {{ matiere.coefficient }})\n              </th>\n              <th rowspan=\"2\">Moyenne</th>\n              <th rowspan=\"2\">Rang</th>\n            </tr>\n            <tr>\n              <template v-for=\"matiere in matieres\" :key=\"`sub-${matiere.id}`\">\n                <th>DS1</th>\n                <th>DS2</th>\n                <th>Examen</th>\n              </template>\n            </tr>\n          </thead>\n          <tbody>\n            <tr v-for=\"(eleve) in eleves\" :key=\"eleve.id\">\n              <td class=\"sticky-col\">{{ eleve.nom }} {{ eleve.prenom }}</td>\n              \n              <template v-for=\"matiere in matieres\" :key=\"`note-${eleve.id}-${matiere.id}`\">\n                <td>\n                  <input \n                    type=\"number\" \n                    min=\"0\" \n                    max=\"20\" \n                    step=\"0.25\"\n                    v-model=\"notesData[eleve.id][matiere.id].ds1\"\n                    @change=\"calculateAverages()\"\n                    :disabled=\"examenVerrouille\"\n                  >\n                </td>\n                <td>\n                  <input \n                    type=\"number\" \n                    min=\"0\" \n                    max=\"20\" \n                    step=\"0.25\"\n                    v-model=\"notesData[eleve.id][matiere.id].ds2\"\n                    @change=\"calculateAverages()\"\n                    :disabled=\"examenVerrouille\"\n                  >\n                </td>\n                <td>\n                  <input \n                    type=\"number\" \n                    min=\"0\" \n                    max=\"20\" \n                    step=\"0.25\"\n                    v-model=\"notesData[eleve.id][matiere.id].examen\"\n                    @change=\"calculateAverages()\"\n                    :disabled=\"examenVerrouille\"\n                  >\n                </td>\n              </template>\n              \n              <td class=\"moyenne\">{{ moyennes[eleve.id] ? moyennes[eleve.id].toFixed(2) : '-' }}</td>\n              <td class=\"rang\">{{ rangs[eleve.id] || '-' }}</td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n      \n      <div class=\"form-actions mt-3\">\n        <button @click=\"saveNotes\" class=\"btn btn-primary\" :disabled=\"examenVerrouille\">\n          Enregistrer les notes\n        </button>\n        <button v-if=\"!examenVerrouille\" @click=\"validateNotes\" class=\"btn btn-success\">\n          Valider et verrouiller\n        </button>\n      </div>\n    </div>\n    \n    <div v-if=\"examenVerrouille && notesLoaded\" class=\"alert alert-warning mt-4\">\n      <i class=\"fas fa-lock\"></i> Cet examen est verrouillé. Les notes ne peuvent plus être modifiées.\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'NotesGestion',\n  data() {\n    return {\n      selectedExamenId: '',\n      selectedClasseId: '',\n      notesLoaded: false,\n      examenVerrouille: false,\n      \n      examens: [\n        { \n          id: 1, \n          type: 'Trimestre 1', \n          dateDebut: '2024-09-15', \n          dateFin: '2024-10-30', \n          classesIds: [1, 2], \n          verrouille: true \n        },\n        { \n          id: 2, \n          type: 'Trimestre 1', \n          dateDebut: '2024-09-15', \n          dateFin: '2024-10-30', \n          classesIds: [3, 4], \n          verrouille: false \n        }\n      ],\n      \n      classes: [\n        { id: 1, nom: '6ème A', effectif: 35 },\n        { id: 2, nom: '6ème B', effectif: 32 },\n        { id: 3, nom: '5ème A', effectif: 30 },\n        { id: 4, nom: '5ème B', effectif: 28 }\n      ],\n      \n      matieres: [\n        { id: 1, nom: 'Mathématiques', abreviation: 'MATH', coefficient: 4 },\n        { id: 2, nom: 'Physique', abreviation: 'PHY', coefficient: 3 },\n        { id: 3, nom: 'Français', abreviation: 'FR', coefficient: 3 }\n      ],\n      \n      eleves: [\n        { id: 1, nom: 'Dupont', prenom: 'Jean', matricule: 'E001', classeId: 1 },\n        { id: 2, nom: 'Martin', prenom: 'Sophie', matricule: 'E002', classeId: 1 },\n        { id: 3, nom: 'Dubois', prenom: 'Pierre', matricule: 'E003', classeId: 1 }\n      ],\n      \n      notesData: {},\n      moyennes: {},\n      rangs: {}\n    }\n  },\n  \n  computed: {\n    classesForExamen() {\n      if (!this.selectedExamenId) return [];\n      \n      const examen = this.examens.find(e => e.id === this.selectedExamenId);\n      if (!examen) return [];\n      \n      return this.classes.filter(c => examen.classesIds.includes(c.id));\n    },\n    \n    canLoadNotes() {\n      return this.selectedExamenId && this.selectedClasseId;\n    }\n  },\n  \n  methods: {\n    loadNotes() {\n      // Dans une application réelle, on chargerait les données depuis le serveur\n      // Pour l'instant, on simule le chargement avec des données fictives\n      \n      // Vérifier si l'examen est verrouillé\n      const examen = this.examens.find(e => e.id === this.selectedExamenId);\n      this.examenVerrouille = examen ? examen.verrouille : false;\n      \n      // Filtrer les élèves de la classe sélectionnée\n      this.eleves = [\n        { id: 1, nom: 'Dupont', prenom: 'Jean', matricule: 'E001', classeId: this.selectedClasseId },\n        { id: 2, nom: 'Martin', prenom: 'Sophie', matricule: 'E002', classeId: this.selectedClasseId },\n        { id: 3, nom: 'Dubois', prenom: 'Pierre', matricule: 'E003', classeId: this.selectedClasseId }\n      ];\n      \n      // Initialiser les données de notes\n      this.notesData = {};\n      this.eleves.forEach(eleve => {\n        this.notesData[eleve.id] = {};\n        this.matieres.forEach(matiere => {\n          // Générer des notes aléatoires pour la démonstration\n          this.notesData[eleve.id][matiere.id] = {\n            ds1: Math.floor(Math.random() * 10) + 10, // Entre 10 et 19\n            ds2: Math.floor(Math.random() * 10) + 10,\n            examen: Math.floor(Math.random() * 10) + 10\n          };\n        });\n      });\n      \n      this.calculateAverages();\n      this.notesLoaded = true;\n    },\n    \n    calculateAverages() {\n      // Calculer les moyennes pour chaque élève\n      this.moyennes = {};\n      \n      this.eleves.forEach(eleve => {\n        let totalPoints = 0;\n        let totalCoefficients = 0;\n        \n        this.matieres.forEach(matiere => {\n          const notes = this.notesData[eleve.id][matiere.id];\n          const moyenne = (parseFloat(notes.ds1) + parseFloat(notes.ds2) + parseFloat(notes.examen)) / 3;\n          const points = moyenne * matiere.coefficient;\n          \n          totalPoints += points;\n          totalCoefficients += matiere.coefficient;\n        });\n        \n        this.moyennes[eleve.id] = totalPoints / totalCoefficients;\n      });\n      \n      // Calculer les rangs\n      const moyennesArray = Object.entries(this.moyennes)\n        .map(([eleveId, moyenne]) => ({ eleveId: parseInt(eleveId), moyenne }))\n        .sort((a, b) => b.moyenne - a.moyenne);\n      \n      this.rangs = {};\n      moyennesArray.forEach((item, index) => {\n        this.rangs[item.eleveId] = index + 1;\n      });\n    },\n    \n    saveNotes() {\n      // Dans une application réelle, on enverrait les données au serveur\n      alert('Notes enregistrées avec succès !');\n    },\n    \n    validateNotes() {\n      if (confirm('Êtes-vous sûr de vouloir valider et verrouiller ces notes ? Cette action est irréversible.')) {\n        // Dans une application réelle, on enverrait la demande au serveur\n        const examen = this.examens.find(e => e.id === this.selectedExamenId);\n        if (examen) {\n          examen.verrouille = true;\n          this.examenVerrouille = true;\n        }\n        \n        alert('Notes validées et verrouillées avec succès !');\n      }\n    },\n    \n    formatDate(dateString) {\n      const options = { day: '2-digit', month: '2-digit', year: 'numeric' };\n      return new Date(dateString).toLocaleDateString('fr-FR', options);\n    }\n  }\n}\n</script>\n\n<style scoped>\n.notes-container {\n  padding: 1.5rem;\n}\n\nh1 {\n  margin-bottom: 1.5rem;\n  color: #333;\n}\n\n.card {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\nh2 {\n  margin-top: 0;\n  margin-bottom: 1rem;\n  font-size: 1.2rem;\n  color: #333;\n  border-bottom: 1px solid #eee;\n  padding-bottom: 0.5rem;\n}\n\n.selection-form {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 1rem;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\nlabel {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #555;\n}\n\ninput, select {\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 1rem;\n}\n\n.form-actions {\n  grid-column: 1 / -1;\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  margin-top: 1rem;\n}\n\n.btn {\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: background-color 0.3s;\n}\n\n.btn-primary {\n  background-color: #3f51b5;\n  color: white;\n}\n\n.btn-primary:hover {\n  background-color: #303f9f;\n}\n\n.btn-secondary {\n  background-color: #f5f5f5;\n  color: #333;\n}\n\n.btn-secondary:hover {\n  background-color: #e0e0e0;\n}\n\n.btn-success {\n  background-color: #4caf50;\n  color: white;\n}\n\n.btn-success:hover {\n  background-color: #388e3c;\n}\n\n.btn:disabled {\n  background-color: #cccccc;\n  cursor: not-allowed;\n}\n\n.mt-3 {\n  margin-top: 1rem;\n}\n\n.mt-4 {\n  margin-top: 1.5rem;\n}\n\n.card-header-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n  border-bottom: 1px solid #eee;\n  padding-bottom: 0.5rem;\n}\n\n.card-header-actions h2 {\n  margin: 0;\n  border-bottom: none;\n  padding-bottom: 0;\n}\n\n.header-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.table-responsive {\n  overflow-x: auto;\n  max-width: 100%;\n}\n\n.notes-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: 0.9rem;\n}\n\n.notes-table th, .notes-table td {\n  padding: 0.5rem;\n  text-align: center;\n  border: 1px solid #ddd;\n}\n\n.notes-table th {\n  background-color: #f5f5f5;\n  font-weight: 500;\n}\n\n.matiere-header {\n  background-color: #e0e0e0;\n}\n\n.sticky-col {\n  position: sticky;\n  left: 0;\n  background-color: #f5f5f5;\n  z-index: 1;\n  text-align: left;\n  min-width: 150px;\n}\n\n.notes-table input {\n  width: 60px;\n  text-align: center;\n  padding: 0.25rem;\n}\n\n.moyenne, .rang {\n  font-weight: bold;\n  background-color: #f5f5f5;\n}\n\n.alert {\n  padding: 1rem;\n  border-radius: 4px;\n  margin-bottom: 1rem;\n}\n\n.alert-warning {\n  background-color: #fff3cd;\n  color: #856404;\n  border: 1px solid #ffeeba;\n}\n\n.alert i {\n  margin-right: 0.5rem;\n}\n</style>\n", "import { render } from \"./NotesGestion.vue?vue&type=template&id=67544b00&scoped=true\"\nimport script from \"./NotesGestion.vue?vue&type=script&lang=js\"\nexport * from \"./NotesGestion.vue?vue&type=script&lang=js\"\n\nimport \"./NotesGestion.vue?vue&type=style&index=0&id=67544b00&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-67544b00\"]])\n\nexport default __exports__"], "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "for", "id", "_cache", "$event", "$data", "selectedExamenId", "required", "value", "disabled", "_Fragment", "_renderList", "examens", "examen", "type", "_toDisplayString", "$options", "formatDate", "dateDebut", "dateFin", "_hoisted_5", "_hoisted_6", "selectedClasseId", "classesForExamen", "classe", "nom", "effectif", "_hoisted_7", "_hoisted_8", "onClick", "args", "loadNotes", "canLoadNotes", "_hoisted_9", "notesLoaded", "_hoisted_10", "_createStaticVNode", "_hoisted_11", "_hoisted_12", "rowspan", "matieres", "matiere", "colspan", "coefficient", "eleves", "eleve", "_hoisted_13", "prenom", "min", "max", "step", "notesData", "ds1", "onChange", "calculateAverages", "examenVerrouille", "_hoisted_14", "ds2", "_hoisted_15", "_hoisted_16", "_hoisted_17", "moy<PERSON>s", "toFixed", "_hoisted_18", "rangs", "_hoisted_19", "saveNotes", "_hoisted_20", "_createCommentVNode", "validateNotes", "_hoisted_21", "_createTextVNode", "name", "data", "classesIds", "<PERSON><PERSON><PERSON><PERSON>", "classes", "abreviation", "matricule", "classeId", "computed", "this", "find", "e", "filter", "c", "includes", "methods", "for<PERSON>ach", "Math", "floor", "random", "totalPoints", "totalCoefficients", "notes", "moyenne", "parseFloat", "points", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "entries", "map", "eleveId", "parseInt", "sort", "a", "b", "item", "index", "alert", "confirm", "dateString", "options", "day", "month", "year", "Date", "toLocaleDateString", "__exports__", "render"], "sourceRoot": ""}